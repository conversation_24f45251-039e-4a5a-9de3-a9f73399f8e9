import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:bike_rental_app/models/bank_account.dart';

class BankAccountService {
  final CollectionReference bankAccountsCollection = FirebaseFirestore.instance
      .collection('bankAccounts');

  // Get all bank accounts
  Future<List<BankAccount>> getBankAccounts() async {
    try {
      // Chỉ lọc theo isActive mà không sắp xếp để tránh lỗi index
      final QuerySnapshot snapshot =
          await bankAccountsCollection.where('isActive', isEqualTo: true).get();

      // Lấy danh sách tài khoản
      final accounts =
          snapshot.docs.map((doc) {
            return BankAccount.fromMap(
              doc.id,
              doc.data() as Map<String, dynamic>,
            );
          }).toList();

      // Sắp xếp theo thời gian tạo mới nhất trước (client-side sorting)
      accounts.sort((a, b) => b.createdAt.compareTo(a.createdAt));

      return accounts;
    } catch (e) {
      print('Error getting bank accounts: $e');
      return [];
    }
  }

  // Get bank account by ID
  Future<BankAccount?> getBankAccountById(String id) async {
    try {
      final DocumentSnapshot doc = await bankAccountsCollection.doc(id).get();

      if (doc.exists) {
        return BankAccount.fromMap(doc.id, doc.data() as Map<String, dynamic>);
      }
      return null;
    } catch (e) {
      print('Error getting bank account: $e');
      return null;
    }
  }

  // Add new bank account
  Future<BankAccount?> addBankAccount({
    required String bankName,
    required String accountNumber,
    required String accountName,
    required String qrImageUrl,
  }) async {
    try {
      final newAccount = {
        'bankName': bankName,
        'accountNumber': accountNumber,
        'accountName': accountName,
        'qrImageUrl': qrImageUrl,
        'isActive': true,
        'createdAt': Timestamp.now(),
        'updatedAt': null,
      };

      final DocumentReference docRef = await bankAccountsCollection.add(
        newAccount,
      );

      return BankAccount.fromMap(docRef.id, newAccount);
    } catch (e) {
      print('Error adding bank account: $e');
      return null;
    }
  }

  // Update bank account
  Future<bool> updateBankAccount(BankAccount account) async {
    try {
      final updateData = account.toMap();
      updateData['updatedAt'] = Timestamp.now();

      await bankAccountsCollection.doc(account.id).update(updateData);
      return true;
    } catch (e) {
      print('Error updating bank account: $e');
      return false;
    }
  }

  // Delete bank account (soft delete by setting isActive to false)
  Future<bool> deleteBankAccount(String id) async {
    try {
      await bankAccountsCollection.doc(id).update({
        'isActive': false,
        'updatedAt': Timestamp.now(),
      });
      return true;
    } catch (e) {
      print('Error deleting bank account: $e');
      return false;
    }
  }

  // Get default bank account (first active account)
  Future<BankAccount?> getDefaultBankAccount() async {
    try {
      final accounts = await getBankAccounts();
      if (accounts.isNotEmpty) {
        return accounts.first;
      }
      return null;
    } catch (e) {
      print('Error getting default bank account: $e');
      return null;
    }
  }
}
