import 'package:awesome_snackbar_content/awesome_snackbar_content.dart';
import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import 'dart:async';
import 'dart:math';
import 'package:panara_dialogs/panara_dialogs.dart';
import 'package:url_launcher/url_launcher.dart';

class BikeLocationMapScreen extends StatefulWidget {
  final String bikeId;

  const BikeLocationMapScreen({required this.bikeId, super.key});

  @override
  _BikeLocationMapScreenState createState() => _BikeLocationMapScreenState();
}

class _BikeLocationMapScreenState extends State<BikeLocationMapScreen> {
  late LatLng _bikeLocation;
  late MapController _mapController;
  late String _address;
  Timer?
  _timer; // Thay 'late' bằng nullable để tránh lỗi LateInitializationError

  // Hàm random tọa độ và địa chỉ trong TP. <PERSON><PERSON> <PERSON><PERSON><String, dynamic> _generateRandomLocation() {
    final random = Random();
    // Giới hạn tọa độ TP. <PERSON><PERSON>
    const double minLat = 10.6; // Vĩ độ tối thiểu
    const double maxLat = 11.0; // Vĩ độ tối đa
    const double minLng = 106.5; // Kinh độ tối thiểu
    const double maxLng = 106.9; // Kinh độ tối đa

    // Random vĩ độ và kinh độ
    double latitude = minLat + random.nextDouble() * (maxLat - minLat);
    double longitude = minLng + random.nextDouble() * (maxLng - minLng);

    // Tạo địa chỉ giả
    String address = "Đường số ${random.nextInt(50)}, TP. Hồ Chí Minh";

    return {'location': LatLng(latitude, longitude), 'address': address};
  }

  // Cập nhật vị trí mới với tối ưu hiệu suất
  void _updateLocation() {
    if (!mounted) return;

    var randomData = _generateRandomLocation();
    setState(() {
      _bikeLocation = randomData['location'];
      _address = randomData['address'];
    });

    // Di chuyển bản đồ đến vị trí mới - thêm try-catch để tránh lỗi
    try {
      _mapController.move(_bikeLocation, 15.0);
    } catch (e) {
      print('Error moving map: $e');
    }
  }

  @override
  void initState() {
    super.initState();
    _mapController = MapController();

    // Khởi tạo vị trí ban đầu
    var initialData = _generateRandomLocation();
    _bikeLocation = initialData['location'];
    _address = initialData['address'];

    // Thiết lập timer để cập nhật sau mỗi 10 phút - sử dụng thời gian dài hơn để giảm tải
    _timer = Timer.periodic(const Duration(minutes: 30), (timer) {
      if (mounted) {
        _updateLocation();
      }
    });
  }

  // Hàm mở Google Maps với tọa độ hiện tại
  Future<void> _openGoogleMaps() async {
    final lat = _bikeLocation.latitude;
    final lng = _bikeLocation.longitude;

    // Sử dụng địa chỉ văn bản của Trường Đại học Công Thương TP.HCM
    final originAddress =
        'Trường Đại học Công Thương TP.HCM, 140 Lê Trọng Tấn, Tây Thạnh, Tân Phú, Thành phố Hồ Chí Minh';
    final encodedOrigin = Uri.encodeComponent(originAddress);

    // Tạo URL với cả vị trí xuất phát và đích đến
    final url =
        'https://www.google.com/maps/dir/?api=1'
        '&origin=$encodedOrigin'
        '&destination=$lat,$lng'
        '&travelmode=driving';

    final Uri uri = Uri.parse(url);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri, mode: LaunchMode.externalApplication);
    } else {
      // Hiển thị thông báo lỗi nếu không thể mở Google Maps
      if (!mounted) return;
      final snackBar = SnackBar(
        elevation: 0,
        backgroundColor: Colors.transparent,
        behavior: SnackBarBehavior.floating,
        content: AwesomeSnackbarContent(
          title: 'Không thể mở Google Maps',
          message:
              'Không thể mở ứng dụng Google Maps. Vui lòng kiểm tra lại thiết bị của bạn.',
          contentType: ContentType.failure,
        ),
      );
      ScaffoldMessenger.of(context)
        ..hideCurrentSnackBar()
        ..showSnackBar(snackBar);
    }
  }

  @override
  void dispose() {
    _timer?.cancel(); // Hủy timer nếu nó tồn tại
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Vị trí xe máy')),
      body: Column(
        children: [
          Expanded(
            child: FlutterMap(
              mapController: _mapController,
              options: MapOptions(
                initialCenter: _bikeLocation,
                initialZoom: 15.0,
                // Thêm các tùy chọn để tối ưu hiệu suất
                interactionOptions: const InteractionOptions(
                  flags: InteractiveFlag.all & ~InteractiveFlag.rotate,
                  enableMultiFingerGestureRace: true,
                ),
                // Giới hạn zoom để tránh sử dụng quá nhiều bộ nhớ
                minZoom: 4.0,
                maxZoom: 18.0,
              ),
              children: [
                TileLayer(
                  urlTemplate: "https://tile.openstreetmap.org/{z}/{x}/{y}.png",
                  // Thêm các tùy chọn để tối ưu hiệu suất
                  tileProvider: NetworkTileProvider(),
                  maxZoom: 18.0,
                  // Sử dụng bộ nhớ đệm để giảm tải mạng
                  keepBuffer: 5,
                ),
                MarkerLayer(
                  markers: [
                    Marker(
                      point: _bikeLocation,
                      child: GestureDetector(
                        onTap: () {
                          PanaraInfoDialog.show(
                            context,
                            title: "Xe máy ID: ${widget.bikeId}",
                            message: _address,
                            buttonText: "Đóng",
                            onTapDismiss: () => Navigator.pop(context),
                            panaraDialogType: PanaraDialogType.error,
                            barrierDismissible: true,
                          );
                        },
                        child: const Icon(
                          Icons.location_pin,
                          color: Colors.red,
                          size: 40.0,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.all(16),
            color: Theme.of(context).colorScheme.surface,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text(
                  'Thông tin vị trí',
                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 18),
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    const Icon(Icons.location_on, color: Colors.red),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            _address, // Sử dụng _address
                            style: const TextStyle(fontWeight: FontWeight.bold),
                          ),
                          Text(
                            '${_bikeLocation.latitude.toStringAsFixed(4)}, ${_bikeLocation.longitude.toStringAsFixed(4)}', // Sử dụng _bikeLocation
                            style: TextStyle(color: Colors.grey[600]),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton.icon(
                    icon: const Icon(Icons.directions),
                    label: const Text('Chỉ đường đến xe'),
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                    onPressed: _openGoogleMaps,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
