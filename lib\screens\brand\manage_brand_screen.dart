import 'package:awesome_snackbar_content/awesome_snackbar_content.dart';
import 'package:bike_rental_app/models/brand.dart';
import 'package:bike_rental_app/services/brand_service.dart';
import 'package:bike_rental_app/services/storage_service.dart';
import 'package:bike_rental_app/widgets/custom_text_form_field.dart';
import 'package:flutter/material.dart';
import 'package:bike_rental_app/widgets/common_widgets.dart';
import 'package:panara_dialogs/panara_dialogs.dart';
import 'package:uuid/uuid.dart';
import 'dart:io';
import 'package:image_picker/image_picker.dart';
import 'package:flutter/services.dart';

class ManageBrandScreen extends StatefulWidget {
  final Brand? brand;

  const ManageBrandScreen({super.key, this.brand});

  @override
  _ManageBrandScreenState createState() => _ManageBrandScreenState();
}

class _ManageBrandScreenState extends State<ManageBrandScreen> {
  final _formKey = GlobalKey<FormState>();
  final BrandService _brandService = BrandService();
  final StorageService _storageService = StorageService();
  final ImagePicker _imagePicker = ImagePicker();

  late TextEditingController _nameController;
  late TextEditingController _descriptionController;
  late TextEditingController _logoUrlController;
  late TextEditingController _countryController;

  bool _isLoading = false;
  bool _isSubmitting = false;
  File? _selectedImage;
  bool _showUrlField = false;

  // Danh sách quốc gia phổ biến cho dropdown
  final List<String> _popularCountries = [
    'Việt Nam',
    'Nhật Bản',
    'Đức',
    'Ý',
    'Mỹ',
    'Anh',
    'Pháp',
    'Trung Quốc',
    'Hàn Quốc',
    'Đài Loan',
  ];

  @override
  void initState() {
    super.initState();
    _nameController = TextEditingController(text: widget.brand?.name ?? '');
    _descriptionController = TextEditingController(
      text: widget.brand?.description ?? '',
    );
    _logoUrlController = TextEditingController(
      text: widget.brand?.logoUrl ?? '',
    );
    _countryController = TextEditingController(
      text: widget.brand?.country ?? '',
    );

    // Hiển thị URL field nếu đã có URL
    _showUrlField =
        widget.brand?.logoUrl != null && widget.brand!.logoUrl!.isNotEmpty;
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _logoUrlController.dispose();
    _countryController.dispose();
    super.dispose();
  }

  Future<void> _pickImage() async {
    try {
      final XFile? pickedImage = await _imagePicker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 80,
      );

      if (pickedImage != null) {
        setState(() {
          _selectedImage = File(pickedImage.path);
        });
      }
    } catch (e) {
      final snackBar = SnackBar(
        elevation: 0,
        behavior: SnackBarBehavior.floating,
        backgroundColor: Colors.transparent,
        content: AwesomeSnackbarContent(
          title: 'Không thể chọn ảnh',
          message: 'Không thể chọn ảnh: $e',
          contentType: ContentType.failure,
        ),
      );
      ScaffoldMessenger.of(context)
        ..hideCurrentSnackBar()
        ..showSnackBar(snackBar);
    }
  }

  Future<void> _takePicture() async {
    try {
      final XFile? takenImage = await _imagePicker.pickImage(
        source: ImageSource.camera,
        imageQuality: 80,
      );

      if (takenImage != null) {
        setState(() {
          _selectedImage = File(takenImage.path);
        });
      }
    } catch (e) {
      final snackBar = SnackBar(
        elevation: 0,
        behavior: SnackBarBehavior.floating,
        backgroundColor: Colors.transparent,
        content: AwesomeSnackbarContent(
          title: 'Không thể chụp ảnh',
          message: 'Không thể chụp ảnh: $e',
          contentType: ContentType.failure,
        ),
      );
      ScaffoldMessenger.of(context)
        ..hideCurrentSnackBar()
        ..showSnackBar(snackBar);
    }
  }

  void _showImagePickerOptions() {
    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) {
        return SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'Chọn ảnh logo',
                  style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                ),
                SizedBox(height: 20),
                ListTile(
                  leading: Container(
                    padding: EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.blue.withOpacity(0.1),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(Icons.photo_library, color: Colors.blue),
                  ),
                  title: Text('Chọn từ thư viện'),
                  onTap: () {
                    Navigator.pop(context);
                    _pickImage();
                  },
                ),
                ListTile(
                  leading: Container(
                    padding: EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.green.withOpacity(0.1),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(Icons.camera_alt, color: Colors.green),
                  ),
                  title: Text('Chụp ảnh mới'),
                  onTap: () {
                    Navigator.pop(context);
                    _takePicture();
                  },
                ),
                ListTile(
                  leading: Container(
                    padding: EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.purple.withOpacity(0.1),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(Icons.link, color: Colors.purple),
                  ),
                  title: Text('Nhập URL ảnh'),
                  onTap: () {
                    Navigator.pop(context);
                    setState(() {
                      _showUrlField = true;
                    });
                  },
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Future<void> _saveBrand() async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isSubmitting = true;
      });

      try {
        String? logoUrl = _logoUrlController.text;

        // Upload image if selected
        if (_selectedImage != null) {
          final String brandId = widget.brand?.id ?? Uuid().v4();
          final String fileName =
              'brand_logo_${brandId}_${DateTime.now().millisecondsSinceEpoch}';
          logoUrl = await _storageService.uploadFile(_selectedImage!, fileName);
        }

        Brand brand = Brand(
          id: widget.brand?.id ?? Uuid().v4(),
          name: _nameController.text,
          description: _descriptionController.text,
          logoUrl: logoUrl.isEmpty ? null : logoUrl,
          country: _countryController.text,
        );

        if (widget.brand == null) {
          await _brandService.addBrand(brand);
          _showSuccessSnackBar('Thêm thương hiệu thành công!');
        } else {
          await _brandService.updateBrand(brand);
          _showSuccessSnackBar('Cập nhật thương hiệu thành công!');
        }
        Navigator.pop(context);
      } catch (e) {
        _showErrorSnackBar('Lỗi: $e');
      } finally {
        if (mounted) {
          setState(() {
            _isSubmitting = false;
          });
        }
      }
    }
  }

  void _showSuccessSnackBar(String message) {
    final snackBar = SnackBar(
      elevation: 0,
      behavior: SnackBarBehavior.floating,
      backgroundColor: Colors.transparent,
      content: AwesomeSnackbarContent(
        title: 'Thành công',
        message: message,
        contentType: ContentType.success,
      ),
    );
    ScaffoldMessenger.of(context)
      ..hideCurrentSnackBar()
      ..showSnackBar(snackBar);
  }

  void _showErrorSnackBar(String message) {
    final snackBar = SnackBar(
      elevation: 0,
      behavior: SnackBarBehavior.floating,
      backgroundColor: Colors.transparent,
      content: AwesomeSnackbarContent(
        title: 'Lỗi',
        message: message,
        contentType: ContentType.failure,
      ),
    );
    ScaffoldMessenger.of(context)
      ..hideCurrentSnackBar()
      ..showSnackBar(snackBar);
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        title: Text(
          widget.brand == null ? 'Thêm thương hiệu' : 'Cập nhật thương hiệu',
          style: theme.textTheme.titleLarge,
        ),
        actions: [
          if (widget.brand != null)
            IconButton(
              icon: Icon(Icons.delete),
              onPressed: () {
                _showDeleteConfirmation();
              },
            ),
        ],
      ),
      body:
          _isLoading
              ? Center(
                child: AppLoadingIndicator(color: theme.primaryColor, size: 30),
              )
              : GestureDetector(
                onTap: () => FocusScope.of(context).unfocus(),
                child: Container(
                  height: double.infinity,
                  decoration: BoxDecoration(
                    color: theme.scaffoldBackgroundColor,
                  ),
                  child: SingleChildScrollView(
                    padding: EdgeInsets.all(20),
                    child: Form(
                      key: _formKey,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Image picker section
                          Center(
                            child: Column(
                              children: [
                                GestureDetector(
                                  onTap: _showImagePickerOptions,
                                  child: Container(
                                    height: 120,
                                    width: 120,
                                    decoration: BoxDecoration(
                                      color: theme.cardColor,
                                      borderRadius: BorderRadius.circular(10),
                                      border: Border.all(
                                        color: theme.dividerColor,
                                      ),
                                    ),
                                    child:
                                        (_selectedImage != null ||
                                                (widget.brand?.logoUrl !=
                                                        null &&
                                                    widget
                                                        .brand!
                                                        .logoUrl!
                                                        .isNotEmpty))
                                            ? ClipRRect(
                                              borderRadius:
                                                  BorderRadius.circular(10),
                                              child:
                                                  _selectedImage != null
                                                      ? Image.file(
                                                        _selectedImage!,
                                                        fit: BoxFit.cover,
                                                      )
                                                      : Image.network(
                                                        widget.brand!.logoUrl!,
                                                        fit: BoxFit.cover,
                                                        loadingBuilder: (
                                                          context,
                                                          child,
                                                          loadingProgress,
                                                        ) {
                                                          if (loadingProgress ==
                                                              null)
                                                            return child;
                                                          return Center(
                                                            child: CircularProgressIndicator(
                                                              value:
                                                                  loadingProgress
                                                                              .expectedTotalBytes !=
                                                                          null
                                                                      ? loadingProgress
                                                                              .cumulativeBytesLoaded /
                                                                          loadingProgress
                                                                              .expectedTotalBytes!
                                                                      : null,
                                                            ),
                                                          );
                                                        },
                                                        errorBuilder: (
                                                          context,
                                                          error,
                                                          stackTrace,
                                                        ) {
                                                          return Center(
                                                            child: Icon(
                                                              Icons
                                                                  .broken_image,
                                                              size: 40,
                                                              color:
                                                                  theme
                                                                      .hintColor,
                                                            ),
                                                          );
                                                        },
                                                      ),
                                            )
                                            : Column(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.center,
                                              children: [
                                                Icon(
                                                  Icons.add_photo_alternate,
                                                  size: 40,
                                                  color: theme.hintColor,
                                                ),
                                                SizedBox(height: 8),
                                                Text(
                                                  'Thêm logo',
                                                  style: theme
                                                      .textTheme
                                                      .bodyMedium
                                                      ?.copyWith(
                                                        color: theme.hintColor,
                                                      ),
                                                ),
                                              ],
                                            ),
                                  ),
                                ),
                                SizedBox(height: 10),
                                TextButton.icon(
                                  icon: Icon(Icons.photo_camera),
                                  label: Text('Chọn ảnh khác'),
                                  style: TextButton.styleFrom(
                                    foregroundColor: theme.primaryColor,
                                  ),
                                  onPressed: _showImagePickerOptions,
                                ),
                              ],
                            ),
                          ),

                          if (_showUrlField)
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                SizedBox(height: 10),
                                Text(
                                  'URL Logo',
                                  style: theme.textTheme.titleSmall,
                                ),
                                SizedBox(height: 6),
                                Stack(
                                  children: [
                                    CustomTextFormField(
                                      label: 'URL Logo',
                                      controller: _logoUrlController,
                                      hintText: 'Nhập URL ảnh logo',
                                      prefixIcon: Icons.link,
                                    ),
                                    Positioned(
                                      right: 0,
                                      top: 0,
                                      bottom: 0,
                                      child: IconButton(
                                        icon: Icon(Icons.close),
                                        onPressed: () {
                                          setState(() {
                                            _logoUrlController.text = '';
                                            _showUrlField = false;
                                          });
                                        },
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),

                          SizedBox(height: 20),
                          Text(
                            'Thông tin cơ bản',
                            style: theme.textTheme.titleLarge,
                          ),

                          SizedBox(height: 16),
                          Text(
                            'Tên thương hiệu',
                            style: theme.textTheme.titleSmall,
                          ),
                          SizedBox(height: 6),
                          CustomTextFormField(
                            label: 'Tên thương hiệu',
                            controller: _nameController,
                            hintText: 'Nhập tên thương hiệu',
                            prefixIcon: Icons.business,
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Vui lòng nhập tên thương hiệu';
                              }
                              return null;
                            },
                          ),

                          SizedBox(height: 16),
                          Text('Quốc gia', style: theme.textTheme.titleSmall),
                          SizedBox(height: 6),
                          CustomTextFormField(
                            controller: _countryController,
                            label: 'Quốc gia xuất xứ',
                            hintText: 'Nhập quốc gia xuất xứ',
                            prefixIcon: Icons.public,
                            filled: true,
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Vui lòng nhập quốc gia';
                              }
                              return null;
                            },
                            suffixIcon: PopupMenuButton<String>(
                              icon: Icon(
                                Icons.arrow_drop_down,
                                color: Theme.of(context).colorScheme.primary,
                              ),
                              onSelected: (String value) {
                                setState(() {
                                  _countryController.text = value;
                                });
                              },
                              itemBuilder: (BuildContext context) {
                                return _popularCountries
                                    .map<PopupMenuItem<String>>((String value) {
                                      return PopupMenuItem<String>(
                                        value: value,
                                        child: Text(value),
                                      );
                                    })
                                    .toList();
                              },
                            ),
                          ),

                          SizedBox(height: 16),
                          Text('Mô tả', style: theme.textTheme.titleSmall),
                          SizedBox(height: 6),
                          CustomTextFormField(
                            controller: _descriptionController,
                            label: 'Mô tả thương hiệu',
                            hintText: 'Nhập mô tả về thương hiệu',
                            filled: true,
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Vui lòng nhập mô tả';
                              }
                              return null;
                            },
                            maxLines: 4,
                          ),

                          SizedBox(height: 30),
                          SizedBox(
                            width: double.infinity,
                            height: 50,
                            child: ElevatedButton(
                              onPressed: _isSubmitting ? null : _saveBrand,
                              style: ElevatedButton.styleFrom(
                                backgroundColor: theme.primaryColor,
                                foregroundColor: Colors.white,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(10),
                                ),
                              ),
                              child:
                                  _isSubmitting
                                      ? Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          SizedBox(
                                            width: 20,
                                            height: 20,
                                            child: AppLoadingIndicator(
                                              color:
                                                  theme.colorScheme.onPrimary,
                                              size: 20,
                                              type:
                                                  LoadingIndicatorType
                                                      .fourRotatingDots,
                                            ),
                                          ),
                                          SizedBox(width: 12),
                                          Text(
                                            'Đang xử lý...',
                                            style: theme.textTheme.titleMedium
                                                ?.copyWith(
                                                  color:
                                                      theme
                                                          .colorScheme
                                                          .onPrimary,
                                                ),
                                          ),
                                        ],
                                      )
                                      : Text(
                                        widget.brand == null
                                            ? 'Thêm thương hiệu'
                                            : 'Cập nhật thương hiệu',
                                        style: theme.textTheme.titleMedium
                                            ?.copyWith(
                                              color:
                                                  theme.colorScheme.onPrimary,
                                            ),
                                      ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
    );
  }

  void _showDeleteConfirmation() {
    PanaraConfirmDialog.show(
      context,
      title: "Xác nhận xóa",
      message: "Bạn có chắc chắn muốn xóa thương hiệu ${widget.brand?.name}?",
      confirmButtonText: "Xóa",
      cancelButtonText: "Hủy",
      textColor: Theme.of(context).primaryColor,
      onTapCancel: () {
        Navigator.pop(context);
      },
      onTapConfirm: () async {
        Navigator.pop(context);
        setState(() {
          _isLoading = true;
        });
        try {
          await _brandService.deleteBrand(widget.brand!.id);
          Navigator.of(context).pop();
          final snackBar = SnackBar(
            elevation: 0,
            behavior: SnackBarBehavior.floating,
            backgroundColor: Colors.transparent,
            content: AwesomeSnackbarContent(
              title: 'Đã xóa',
              message: 'Đã xóa thương hiệu ${widget.brand!.name}',
              contentType: ContentType.success,
            ),
          );
          ScaffoldMessenger.of(context)
            ..hideCurrentSnackBar()
            ..showSnackBar(snackBar);
        } catch (e) {
          setState(() {
            _isLoading = false;
          });
          final snackBar = SnackBar(
            elevation: 0,
            behavior: SnackBarBehavior.floating,
            backgroundColor: Colors.transparent,
            content: AwesomeSnackbarContent(
              title: 'Lỗi',
              message: 'Đã xảy ra lỗi khi xóa thương hiệu',
              contentType: ContentType.failure,
            ),
          );
          ScaffoldMessenger.of(context)
            ..hideCurrentSnackBar()
            ..showSnackBar(snackBar);
        }
      },
      panaraDialogType: PanaraDialogType.custom,
      color: Theme.of(context).primaryColor,
      barrierDismissible: false,
    );
  }
}
