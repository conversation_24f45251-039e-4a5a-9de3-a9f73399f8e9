import 'package:bike_rental_app/services/rental_service.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:timezone/data/latest.dart' as tz_data;
import '../models/notification.dart';
import '../models/rental.dart';
import '../services/user_service.dart';
import '../services/email_service.dart';

class NotificationService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FlutterLocalNotificationsPlugin _localNotifications =
      FlutterLocalNotificationsPlugin();

  // Collection references
  CollectionReference get _notificationsRef =>
      _firestore.collection('notifications');
  CollectionReference get _rentalsRef => _firestore.collection('rentals');

  // Khởi tạo service
  Future<void> init() async {
    // Initialize timezone
    tz_data.initializeTimeZones();

    try {
      // Yêu cầu quyền cho local notifications
      await _requestLocalPermissions();

      // Khởi tạo local notifications
      await _initializeLocalNotifications();

      // <PERSON><PERSON>m tra các đơn thuê sắp đến hạn và đã quá hạn
      await checkDueRentals();
      await checkExpiredRentals();
    } catch (e) {
      print('Error initializing notifications: $e');
    }
  }

  // Yêu cầu quyền cho local notifications
  Future<void> _requestLocalPermissions() async {
    try {
      // Thêm quyền trên Android 13+
      await _localNotifications
          .resolvePlatformSpecificImplementation<
            AndroidFlutterLocalNotificationsPlugin
          >()
          ?.requestNotificationsPermission();

      // Yêu cầu quyền trên iOS
      print('Local notification permissions requested');
    } catch (e) {
      print('Error requesting local notification permissions: $e');
    }
  }

  Future<void> _initializeLocalNotifications() async {
    try {
      const AndroidInitializationSettings androidSettings =
          AndroidInitializationSettings('@mipmap/ic_launcher');
      const DarwinInitializationSettings iosSettings =
          DarwinInitializationSettings(
            requestAlertPermission: true,
            requestBadgePermission: true,
            requestSoundPermission: true,
          );

      const InitializationSettings initSettings = InitializationSettings(
        android: androidSettings,
        iOS: iosSettings,
      );

      await _localNotifications.initialize(
        initSettings,
        onDidReceiveNotificationResponse: (NotificationResponse response) {
          print('Notification clicked: ${response.payload}');
        },
      );
    } catch (e) {
      print('Error initializing local notifications: $e');
    }
  }

  // Show a local notification
  Future<void> _showLocalNotification(
    String title,
    String body, {
    String? payload,
  }) async {
    try {
      const AndroidNotificationDetails androidDetails =
          AndroidNotificationDetails(
            'rental_notifications',
            'Rental Notifications',
            channelDescription: 'Notifications for bike rentals',
            importance: Importance.high,
            priority: Priority.high,
          );

      const DarwinNotificationDetails iosDetails = DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      );

      const NotificationDetails notificationDetails = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      await _localNotifications.show(
        DateTime.now().millisecond,
        title,
        body,
        notificationDetails,
        payload: payload,
      );
    } catch (e) {
      print('Error showing local notification: $e');
    }
  }

  // Kiểm tra và thông báo các đơn thuê sắp đến hạn
  Future<void> checkDueRentals() async {
    try {
      final now = DateTime.now();
      final tomorrow = now.add(const Duration(days: 1));

      final QuerySnapshot snapshot =
          await _rentalsRef
              .where('endTime', isGreaterThanOrEqualTo: Timestamp.fromDate(now))
              .where(
                'endTime',
                isLessThanOrEqualTo: Timestamp.fromDate(tomorrow),
              )
              .where('status', isEqualTo: 'Ongoing')
              .get();

      // Import các service cần thiết
      final userService = UserService();
      final emailService = EmailService();

      for (final doc in snapshot.docs) {
        final rental = Rental.fromMap(
          doc.id,
          doc.data() as Map<String, dynamic>,
        );

        final existingNotifications =
            await _notificationsRef
                .where('rentalId', isEqualTo: rental.id)
                .where('type', isEqualTo: 'rental_due')
                .get();

        if (existingNotifications.docs.isEmpty) {
          // Ở đây, userId không còn ý nghĩa lọc theo user,
          // nhưng ta vẫn giữ lại để biết ai đã thuê (nếu cần).
          await createNotification(
            userId: rental.userId,
            rentalId: rental.id,
            title: 'Đơn thuê sắp đến hạn',
            body: 'Đơn thuê ${rental.id} sẽ đến hạn vào ngày mai!',
            type: 'rental_due',
          );

          await _showLocalNotification(
            'Đơn thuê sắp đến hạn',
            'Đơn thuê ${rental.id} sắp đến hạn vào ngày mai!',
            payload: rental.id,
          );

          // Gửi email thông báo đơn thuê sắp hết hạn
          try {
            // Lấy thông tin người dùng
            final user = await userService.getUserById(rental.userId);
            if (user != null) {
              // Gửi email
              await emailService.sendRentalDueNotification(
                rental: rental,
                user: user,
              );
            }
          } catch (emailError) {
            print('Error sending due rental email: $emailError');
            // Không throw lỗi ở đây để không ảnh hưởng đến luồng chính
          }
        }
      }
    } catch (e) {
      print('Error checking due rentals: $e');
    }
  }

  // Kiểm tra và thông báo các đơn thuê đã quá hạn
  Future<void> checkExpiredRentals() async {
    try {
      final now = DateTime.now();
      final userService = UserService();
      final emailService = EmailService();
      final rentalService = RentalService();

      // Sử dụng phương thức từ RentalService để cập nhật trạng thái đơn thuê quá hạn
      final expiredRentals = await rentalService.checkAndUpdateExpiredRentals();

      // Xử lý thông báo cho từng đơn thuê quá hạn
      for (final rental in expiredRentals) {
        // Tính thời gian quá hạn
        final difference = now.difference(rental.endTime);
        String overdueText;

        if (difference.inDays < 1) {
          // Nếu chưa đủ 1 ngày, hiển thị theo giờ
          final hoursOverdue = difference.inHours;
          overdueText = '$hoursOverdue giờ';
        } else {
          // Nếu từ 1 ngày trở lên, hiển thị theo ngày
          final daysOverdue = difference.inDays;
          overdueText = '$daysOverdue ngày';
        }

        // Kiểm tra xem đã có thông báo quá hạn ban đầu cho đơn thuê này chưa
        final existingNotifications =
            await _notificationsRef
                .where('rentalId', isEqualTo: rental.id)
                .where('type', isEqualTo: 'rental_expired')
                .get();

        // Kiểm tra xem đã có thông báo nhắc nhở hàng ngày cho đơn thuê này trong ngày hôm nay chưa
        final today = DateTime(now.year, now.month, now.day);
        final tomorrow = today.add(const Duration(days: 1));

        final dailyReminders =
            await _notificationsRef
                .where('rentalId', isEqualTo: rental.id)
                .where('type', isEqualTo: 'rental_daily_reminder')
                .where(
                  'createdAt',
                  isGreaterThanOrEqualTo: Timestamp.fromDate(today),
                )
                .where('createdAt', isLessThan: Timestamp.fromDate(tomorrow))
                .get();

        // Nếu chưa có thông báo ban đầu, tạo thông báo mới
        if (existingNotifications.docs.isEmpty) {
          await createNotification(
            userId: rental.userId,
            rentalId: rental.id,
            title: 'Đơn thuê đã quá hạn',
            body:
                'Đơn thuê ${rental.id} đã quá hạn $overdueText. Hãy kiểm tra đơn thuê và liên hệ khách hàng!',
            type: 'rental_expired',
          );

          await _showLocalNotification(
            'Đơn thuê đã quá hạn',
            'Đơn thuê ${rental.id} đã quá hạn $overdueText. Hãy kiểm tra đơn thuê và liên hệ khách hàng!',
            payload: rental.id,
          );

          // Gửi email thông báo đơn thuê đã quá hạn
          try {
            // Lấy thông tin người dùng
            final user = await userService.getUserById(rental.userId);
            if (user != null) {
              // Gửi email
              await emailService.sendRentalExpiredNotification(
                rental: rental,
                user: user,
              );
            }
          } catch (emailError) {
            print('Error sending expired rental email: $emailError');
            // Không throw lỗi ở đây để không ảnh hưởng đến luồng chính
          }
        }
        // Nếu đã có thông báo ban đầu nhưng chưa có thông báo nhắc nhở hàng ngày, tạo thông báo nhắc nhở
        else if (dailyReminders.docs.isEmpty &&
            existingNotifications.docs.isNotEmpty) {
          // Tạo thông báo nhắc nhở hàng ngày
          await createNotification(
            userId: rental.userId,
            rentalId: rental.id,
            title: 'Nhắc nhở: Đơn thuê vẫn đang quá hạn',
            body:
                'Đơn thuê ${rental.id} vẫn đang quá hạn $overdueText. Vui lòng liên hệ khách hàng ngay!',
            type: 'rental_daily_reminder',
          );

          await _showLocalNotification(
            'Nhắc nhở: Đơn thuê vẫn đang quá hạn',
            'Đơn thuê ${rental.id} vẫn đang quá hạn $overdueText. Vui lòng liên hệ khách hàng ngay!',
            payload: rental.id,
          );

          // Gửi email nhắc nhở hàng ngày
          try {
            // Lấy thông tin người dùng
            final user = await userService.getUserById(rental.userId);
            if (user != null) {
              // Gửi email
              await emailService.sendRentalExpiredNotification(
                rental: rental,
                user: user,
              );
            }
          } catch (emailError) {
            print('Error sending daily reminder email: $emailError');
          }
        }
      }
    } catch (e) {
      print('Error checking expired rentals: $e');
    }
  }

  /// Tạo thông báo mới trên Firestore
  Future<BikeNotification> createNotification({
    required String userId, // Giữ lại để biết ai đã thuê
    required String rentalId,
    required String title,
    required String body,
    required String type,
  }) async {
    final data = {
      'userId': userId,
      'rentalId': rentalId,
      'title': title,
      'body': body,
      'createdAt': Timestamp.now(),
      'isRead': false,
      'type': type,
    };

    final docRef = await _notificationsRef.add(data);
    return BikeNotification.fromMap(docRef.id, data);
  }

  /// Lấy tất cả thông báo (dành cho Admin)
  Stream<List<BikeNotification>> getAllNotifications() {
    return _notificationsRef
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map((snapshot) {
          return snapshot.docs.map((doc) {
            return BikeNotification.fromMap(
              doc.id,
              doc.data() as Map<String, dynamic>,
            );
          }).toList();
        });
  }

  /// Đánh dấu một thông báo là đã đọc
  Future<void> markAsRead(String notificationId) async {
    await _notificationsRef.doc(notificationId).update({'isRead': true});
  }

  /// Đánh dấu **tất cả** thông báo là đã đọc (cho Admin)
  Future<void> markAllAsRead() async {
    final batch = _firestore.batch();
    final snapshot =
        await _notificationsRef.where('isRead', isEqualTo: false).get();

    for (final doc in snapshot.docs) {
      batch.update(doc.reference, {'isRead': true});
    }
    await batch.commit();
  }

  /// Xoá một thông báo
  Future<void> deleteNotification(String notificationId) async {
    await _notificationsRef.doc(notificationId).delete();
  }
}
