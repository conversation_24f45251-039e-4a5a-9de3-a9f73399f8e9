import 'dart:async';
import 'dart:math';
import '../models/payment.dart';

// Enum to represent payment status as defined by a typical payment gateway
enum PaymentStatus {
  pending,
  processing,
  authorized,
  completed,
  failed,
  refunded,
  cancelled,
}

// Enum to represent different payment methods
enum PaymentMethod { cash, creditCard, bankTransfer, eWallet }

// Class to simulate payment gateway responses
class PaymentResponse {
  final bool success;
  final String transactionId;
  final PaymentStatus status;
  final String? errorMessage;
  final DateTime timestamp;
  final Map<String, dynamic>? additionalInfo;

  PaymentResponse({
    required this.success,
    required this.transactionId,
    required this.status,
    this.errorMessage,
    required this.timestamp,
    this.additionalInfo,
  });
}

// VNPay response simulation class
class VNPayResponse {
  final bool success;
  final String transactionNo;
  final String responseCode;
  final String orderInfo;
  final String bankCode;
  final String cardType;
  final DateTime timestamp;
  final String? errorMessage;

  VNPayResponse({
    required this.success,
    required this.transactionNo,
    required this.responseCode,
    required this.orderInfo,
    required this.bankCode,
    required this.cardType,
    required this.timestamp,
    this.errorMessage,
  });
}

// Main class for the mock payment gateway
class MockPaymentGateway {
  // Singleton instance
  static final MockPaymentGateway _instance = MockPaymentGateway._internal();
  factory MockPaymentGateway() => _instance;
  MockPaymentGateway._internal();

  // Method to process payment
  Future<PaymentResponse> processPayment({
    required String paymentMethod,
    required double amount,
    required String orderId,
    Map<String, dynamic>? additionalData,
  }) async {
    // Simulate network delay
    await Future.delayed(Duration(seconds: 2));

    // Handle based on payment method
    if (paymentMethod == PaymentMethodConstants.cash) {
      return _processCashPayment(amount, orderId, additionalData);
    } else if (paymentMethod == PaymentMethodConstants.vnpay) {
      // VNPay payments are handled through the VNPay portal
      // This method should not be called directly for VNPay
      throw Exception(
        'VNPay payments should be processed through initVNPayTransaction',
      );
    } else {
      // Unsupported payment method
      return PaymentResponse(
        success: false,
        transactionId: 'ERROR-${DateTime.now().millisecondsSinceEpoch}',
        status: PaymentStatus.failed,
        errorMessage: 'Unsupported payment method',
        timestamp: DateTime.now(),
      );
    }
  }

  // Method to process cash payment
  Future<PaymentResponse> _processCashPayment(
    double amount,
    String orderId,
    Map<String, dynamic>? additionalData,
  ) async {
    // Cash payments are always successful in the mock
    final transactionId =
        'CASH-${DateTime.now().millisecondsSinceEpoch}-${Random().nextInt(1000)}';

    return PaymentResponse(
      success: true,
      transactionId: transactionId,
      status: PaymentStatus.completed,
      timestamp: DateTime.now(),
      additionalInfo: {
        'paymentMethod': PaymentMethodConstants.cash,
        'amount': amount,
        'orderId': orderId,
        'processingTime': '0.5 seconds',
        ...?additionalData,
      },
    );
  }

  // Generate VNPay QR code data
  Future<Map<String, dynamic>> generateVNPayQRData({
    required double amount,
    required String orderId,
    String? description,
  }) async {
    // Simulate network delay
    await Future.delayed(Duration(seconds: 1));

    // In a real implementation, this would call VNPay API to generate QR code
    // For now, we'll return a mock QR data structure
    final qrContent =
        'vnpay://pay?amount=${amount.round()}&orderId=$orderId&desc=${description ?? 'Thanh toán đơn thuê'}';

    return {
      'qrContent': qrContent,
      'amount': amount,
      'orderId': orderId,
      'description': description ?? 'Thanh toán đơn thuê: $orderId',
      'expiryTime': DateTime.now().add(Duration(minutes: 15)),
      'merchantName': 'Smurf Company Rental',
      'merchantId': 'SMURF1234',
    };
  }

  // Phương thức initVNPayTransaction đã được loại bỏ vì không còn sử dụng WebView

  // Process VNPay payment response (simulating callback from VNPay)
  Future<VNPayResponse> processVNPayCallback({
    required String orderId,
    required double amount,
    bool forceSuccess = true, // For testing, allow forcing success or failure
  }) async {
    // Simulate network delay
    await Future.delayed(Duration(seconds: 2));

    final random = Random();
    // Success probability - in real world this comes from VNPay
    final isSuccessful = forceSuccess ? true : random.nextDouble() < 0.9;

    if (isSuccessful) {
      // Bank codes in Vietnam
      final bankCodes = ['NCB', 'BIDV', 'VCB', 'TCB', 'MB', 'VIB', 'VPB'];
      // Card types
      final cardTypes = ['ATM', 'QRCODE', 'INTCARD'];

      return VNPayResponse(
        success: true,
        transactionNo: 'VNP${DateTime.now().millisecondsSinceEpoch}',
        responseCode: '00', // 00 means success in VNPay
        orderInfo: 'Thanh toán đơn thuê: $orderId',
        bankCode: bankCodes[random.nextInt(bankCodes.length)],
        cardType: cardTypes[random.nextInt(cardTypes.length)],
        timestamp: DateTime.now(),
      );
    } else {
      // Error codes from VNPay
      final errorCodes = ['01', '02', '07', '09', '10', '11', '12', '99'];
      final errorMessages = {
        '01': 'Order not found',
        '02': 'Order already paid',
        '07': 'Transaction declined by bank',
        '09': 'Transaction expired',
        '10': 'Technical error',
        '11': 'Customer cancelled',
        '12': 'Invalid amount',
        '99': 'Other error',
      };

      final errorCode = errorCodes[random.nextInt(errorCodes.length)];

      return VNPayResponse(
        success: false,
        transactionNo: 'VNP${DateTime.now().millisecondsSinceEpoch}',
        responseCode: errorCode,
        orderInfo: 'Thanh toán đơn thuê: $orderId',
        bankCode: 'UNKNOWN',
        cardType: 'UNKNOWN',
        timestamp: DateTime.now(),
        errorMessage: errorMessages[errorCode],
      );
    }
  }

  // Method to authorize payment (separate step from capturing)
  Future<PaymentResponse> authorizePayment({
    required String paymentMethod,
    required double amount,
    required String orderId,
  }) async {
    await Future.delayed(Duration(seconds: 1));

    final random = Random();
    final isSuccessful =
        random.nextDouble() < 0.95; // Higher success rate for auth
    final transactionId =
        'AUTH-${DateTime.now().millisecondsSinceEpoch}-${random.nextInt(1000)}';

    if (isSuccessful) {
      return PaymentResponse(
        success: true,
        transactionId: transactionId,
        status: PaymentStatus.authorized,
        timestamp: DateTime.now(),
        additionalInfo: {
          'authCode': 'A${random.nextInt(999999)}',
          'expiresAt': DateTime.now().add(Duration(hours: 24)),
        },
      );
    } else {
      return PaymentResponse(
        success: false,
        transactionId: transactionId,
        status: PaymentStatus.failed,
        errorMessage: 'Authorization failed',
        timestamp: DateTime.now(),
      );
    }
  }

  // Method to capture previously authorized payment
  Future<PaymentResponse> capturePayment({
    required String transactionId,
    required double amount,
  }) async {
    await Future.delayed(Duration(milliseconds: 800));

    final random = Random();
    final isSuccessful =
        random.nextDouble() < 0.98; // Very high success rate for captures

    if (isSuccessful) {
      return PaymentResponse(
        success: true,
        transactionId: transactionId,
        status: PaymentStatus.completed,
        timestamp: DateTime.now(),
        additionalInfo: {'captureCode': 'CAP${random.nextInt(999999)}'},
      );
    } else {
      return PaymentResponse(
        success: false,
        transactionId: transactionId,
        status: PaymentStatus.failed,
        errorMessage: 'Capture failed: authorization expired',
        timestamp: DateTime.now(),
      );
    }
  }

  // Process refund for a payment
  Future<PaymentResponse> refundPayment({
    required String transactionId,
    required double amount,
    String? reason,
  }) async {
    await Future.delayed(Duration(seconds: 2));

    final random = Random();
    final isSuccessful = random.nextDouble() < 0.95;

    if (isSuccessful) {
      return PaymentResponse(
        success: true,
        transactionId: 'REF-$transactionId',
        status: PaymentStatus.refunded,
        timestamp: DateTime.now(),
        additionalInfo: {
          'originalTransaction': transactionId,
          'refundAmount': amount,
          'reason': reason ?? 'Customer request',
        },
      );
    } else {
      return PaymentResponse(
        success: false,
        transactionId: 'REF-$transactionId',
        status: PaymentStatus.failed,
        errorMessage: 'Refund failed: original transaction not found',
        timestamp: DateTime.now(),
      );
    }
  }

  // Simulate a verifying payment by card provider
  Future<bool> verifyCardDetails(
    String cardNumber,
    String expiry,
    String cvv,
  ) async {
    await Future.delayed(Duration(milliseconds: 500));

    // Simple validation logic
    if (cardNumber.length < 13 || cardNumber.length > 19) return false;
    if (!RegExp(r'^\d{2}/\d{2}$').hasMatch(expiry)) return false;
    if (!RegExp(r'^\d{3,4}$').hasMatch(cvv)) return false;

    return Random().nextDouble() < 0.9; // 90% chance of success
  }

  // Process card payment
  Future<PaymentResponse> processCardPayment({
    required String cardNumber,
    required String cardHolder,
    required String expiryDate,
    required String cvv,
    required double amount,
    required String orderId,
  }) async {
    // Simulate network delay
    await Future.delayed(Duration(seconds: 2));

    // Validate card number (simple validation for demo)
    bool isValidCard =
        cardNumber.replaceAll(' ', '').length >= 13 &&
        cardNumber.replaceAll(' ', '').length <= 19;

    // Validate expiry date (simple validation for demo)
    bool isValidExpiry = RegExp(r'^\d{2}/\d{2}$').hasMatch(expiryDate);

    // Validate CVV (simple validation for demo)
    bool isValidCvv = RegExp(r'^\d{3,4}$').hasMatch(cvv);

    // For demo purposes, always succeed with test card number
    bool isTestCard = cardNumber.replaceAll(' ', '') == '****************';

    // Determine success based on validation and test card
    bool success = (isValidCard && isValidExpiry && isValidCvv) || isTestCard;

    // Generate transaction ID
    String transactionId = 'CARD_${DateTime.now().millisecondsSinceEpoch}';

    // Return response
    return PaymentResponse(
      success: success,
      transactionId: transactionId,
      status: success ? PaymentStatus.completed : PaymentStatus.failed,
      errorMessage: success ? null : 'Invalid card information',
      timestamp: DateTime.now(),
      additionalInfo: {
        'paymentMethod': 'Card',
        'orderId': orderId,
        'cardLast4': cardNumber
            .replaceAll(' ', '')
            .substring(cardNumber.replaceAll(' ', '').length - 4),
        'cardType': _getCardType(cardNumber),
      },
    );
  }

  // Helper method to determine card type
  String _getCardType(String cardNumber) {
    // Remove spaces
    cardNumber = cardNumber.replaceAll(' ', '');

    // Check card type based on first digits
    if (cardNumber.startsWith('4')) {
      return 'Visa';
    } else if (cardNumber.startsWith('5')) {
      return 'MasterCard';
    } else if (cardNumber.startsWith('34') || cardNumber.startsWith('37')) {
      return 'American Express';
    } else if (cardNumber.startsWith('6')) {
      return 'Discover';
    } else {
      return 'Unknown';
    }
  }

  // Generate payment receipt data
  Map<String, dynamic> generateReceipt({
    required String transactionId,
    required double amount,
    required String paymentMethod,
    required DateTime timestamp,
    required String orderId,
    Map<String, dynamic>? additionalInfo,
  }) {
    final receiptNumber = 'R-${DateTime.now().millisecondsSinceEpoch}';

    return {
      'receiptNumber': receiptNumber,
      'transactionId': transactionId,
      'amount': amount,
      'paymentMethod': paymentMethod,
      'timestamp': timestamp,
      'orderId': orderId,
      'additionalInfo': additionalInfo,
      'merchantName': 'Smurf Company Rental',
      'merchantAddress': '123 Bike Street, Ho Chi Minh City',
      'merchantTaxId': '**********',
    };
  }

  // Get VNPay demo bank accounts for testing
  List<Map<String, String>> getDemoBankAccounts() {
    return [
      {
        'bankCode': 'NCB',
        'bankName': 'Ngân hàng NCB',
        'cardNumber': '9704198526191432198',
        'accountName': 'NGUYEN VAN A',
        'cardDate': '07/15',
        'otp': '123456',
      },
      {
        'bankCode': 'BIDV',
        'bankName': 'Ngân hàng BIDV',
        'cardNumber': '9704198526191432198',
        'accountName': 'NGUYEN VAN A',
        'cardDate': '07/15',
        'otp': '123456',
      },
      {
        'bankCode': 'VCB',
        'bankName': 'Ngân hàng Vietcombank',
        'cardNumber': '9704198526191432198',
        'accountName': 'NGUYEN VAN A',
        'cardDate': '07/15',
        'otp': '123456',
      },
    ];
  }
}
