import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:loading_animation_widget/loading_animation_widget.dart';

class RESETpasswordPage extends StatefulWidget {
  @override
  State<StatefulWidget> createState() => _RESETpassword();
}

class _RESETpassword extends State<RESETpasswordPage> {
  var email = TextEditingController();
  bool isLoading = false;

  @override
  void initState() {
    super.initState();
    isLoading = false;
  }

  resetPassword() async {
    await FirebaseAuth.instance.sendPasswordResetEmail(
      email: email.text.toString(),
    );
    Navigator.of(context).pop();
  }

  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        //logo công ty
        title: GestureDetector(
          onTap: () {
            Navigator.pushNamed(context, '/info-company');
          },
          child: Stack(
            alignment: Alignment.center,
            children: [
              Image.asset(
                'assets/images/main_logo.png',
                height: 120,
                fit: BoxFit.contain,
              ),
            ],
          ),
        ),
        centerTitle: true,
        iconTheme: IconThemeData(color: Colors.black),
      ),
      backgroundColor: Colors.white,
      body: SingleChildScrollView(
        physics: const BouncingScrollPhysics(),
        child: Padding(
          padding: const EdgeInsets.all(30.0),
          child: Column(
            children: [
              SizedBox(
                width: 350,
                child: Image.asset('assets/images/reset_password.jpg'),
              ),
              Container(
                alignment: Alignment.centerLeft,
                child: const Text(
                  'Quên \nmật khẩu?',
                  style: TextStyle(
                    fontFamily: 'Poppins',
                    fontSize: 40,
                    fontWeight: FontWeight.w700,
                    color: Colors.black,
                  ),
                ),
              ),
              const SizedBox(height: 10),
              Container(
                alignment: Alignment.centerLeft,
                child: Text(
                  "Nhập email của bạn để nhận mã xác thực",
                  style: const TextStyle(fontSize: 17, color: Colors.grey),
                ),
              ),
              const SizedBox(height: 10),
              Container(
                alignment: Alignment.centerRight,
                child: Form(
                  child: TextFormField(
                    controller: email,
                    keyboardType: TextInputType.emailAddress,
                    decoration: InputDecoration(
                      icon: Icon(
                        Icons.alternate_email_rounded,
                        color: Colors.black,
                      ),
                      label: Text(
                        "Email",
                        style: TextStyle(color: Colors.black),
                      ),
                      enabledBorder: const UnderlineInputBorder(
                        borderSide: BorderSide(color: Colors.black),
                      ),
                      focusedBorder: const UnderlineInputBorder(
                        borderSide: BorderSide(color: Colors.black, width: 2),
                      ),
                      fillColor: Colors.white,
                      filled: true,
                    ),
                    style: TextStyle(color: Colors.black),
                  ),
                ),
              ),
              SizedBox(height: 40),
              ElevatedButton(
                onPressed: () async {
                  setState(() {
                    isLoading = true;
                  });
                  try {
                    await resetPassword();
                  } finally {
                    setState(() {
                      isLoading = false;
                    });
                  }
                },
                style: ElevatedButton.styleFrom(
                  minimumSize: const Size.fromHeight(50),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                  backgroundColor: Color.fromARGB(255, 0, 85, 154),
                ),
                child: Center(
                  child:
                      isLoading
                          ? LoadingAnimationWidget.fourRotatingDots(
                            color: Colors.white,
                            size: 20,
                          )
                          : const Text(
                            "Lấy mã",
                            style: TextStyle(fontSize: 15),
                          ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
