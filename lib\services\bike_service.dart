import 'package:bike_rental_app/models/bike.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

class BikeService {
  final CollectionReference bikesCollection = FirebaseFirestore.instance
      .collection('bikes');

  // L<PERSON>y danh sách tất cả xe
  Future<List<Bike>> getBikes() async {
    try {
      QuerySnapshot querySnapshot = await bikesCollection.get();
      return querySnapshot.docs.map((doc) {
        Map<String, dynamic> data = doc.data() as Map<String, dynamic>;
        data['id'] = doc.id;
        return Bike.fromJson(data);
      }).toList();
    } catch (e) {
      print('Error getting bikes: $e');
      throw e;
    }
  }

  // Lấy danh sách xe theo thương hiệu
  Future<List<Bike>> getBikesByBrand(String brandId) async {
    try {
      QuerySnapshot querySnapshot =
          await bikesCollection.where('brandId', isEqualTo: brandId).get();
      return querySnapshot.docs.map((doc) {
        Map<String, dynamic> data = doc.data() as Map<String, dynamic>;
        data['id'] = doc.id;
        return Bike.fromJson(data);
      }).toList();
    } catch (e) {
      print('Error getting bikes by brand: $e');
      throw e;
    }
  }

  // Lấy danh sách xe theo id
  Future<Bike> getBikeById(String id) async {
    try {
      DocumentSnapshot doc = await bikesCollection.doc(id).get();
      if (doc.exists) {
        Map<String, dynamic> data = doc.data() as Map<String, dynamic>;
        data['id'] = doc.id;
        return Bike.fromJson(data);
      } else {
        throw Exception('Bike not found');
      }
    } catch (e) {
      print('Error getting bike: $e');
      throw e;
    }
  }

  // Thêm xe mới
  Future<void> addBike(Bike bike) async {
    try {
      await bikesCollection.doc(bike.id).set(bike.toJson());
    } catch (e) {
      print('Error adding bike: $e');
      throw e;
    }
  }

  // Cập nhật thông tin xe
  Future<void> updateBike(Bike bike) async {
    try {
      await bikesCollection.doc(bike.id).update(bike.toJson());
    } catch (e) {
      print('Error updating bike: $e');
      throw e;
    }
  }

  // Cập nhật số lượng xe
  Future<void> updateBikeQuantity(String id, int newQuantity) async {
    try {
      await bikesCollection.doc(id).update({'quantity': newQuantity});
    } catch (e) {
      print('Error updating bike quantity: $e');
      throw e;
    }
  }

  // Xóa xe
  Future<void> deleteBike(String id) async {
    try {
      await bikesCollection.doc(id).delete();
    } catch (e) {
      print('Error deleting bike: $e');
      throw e;
    }
  }
}
