// import loading_overlay đã bị xóa
import 'package:flutter/material.dart';
import 'package:bike_rental_app/models/bank_account.dart';
import 'package:bike_rental_app/services/bank_account_service.dart';
import 'package:awesome_snackbar_content/awesome_snackbar_content.dart';
import 'package:bike_rental_app/utils/loading_dialog.dart';
import 'package:bike_rental_app/widgets/custom_text_form_field.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';
import 'package:bike_rental_app/services/storage_service.dart';
import 'package:path/path.dart' as path;

class BankAccountManagementScreen extends StatefulWidget {
  const BankAccountManagementScreen({Key? key}) : super(key: key);

  @override
  _BankAccountManagementScreenState createState() =>
      _BankAccountManagementScreenState();
}

class _BankAccountManagementScreenState
    extends State<BankAccountManagementScreen> {
  final BankAccountService _bankAccountService = BankAccountService();
  final _formKey = GlobalKey<FormState>();
  final _bankNameController = TextEditingController();
  final _accountNumberController = TextEditingController();
  final _accountNameController = TextEditingController();

  List<BankAccount> _bankAccounts = [];
  bool _isLoading = true;
  File? _qrImageFile;
  String? _qrImageUrl;
  BankAccount? _editingAccount;

  @override
  void initState() {
    super.initState();
    _loadBankAccounts();
  }

  @override
  void dispose() {
    _bankNameController.dispose();
    _accountNumberController.dispose();
    _accountNameController.dispose();
    super.dispose();
  }

  Future<void> _loadBankAccounts() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final accounts = await _bankAccountService.getBankAccounts();
      setState(() {
        _bankAccounts = accounts;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      _showErrorSnackBar('Không thể tải danh sách tài khoản ngân hàng');
    }
  }

  Future<void> _pickQRImage() async {
    try {
      final ImagePicker picker = ImagePicker();

      // Hiển thị hộp thoại chọn nguồn ảnh
      final source = await showDialog<ImageSource>(
        context: context,
        builder:
            (context) => AlertDialog(
              title: Text('Chọn nguồn ảnh'),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  ListTile(
                    leading: Icon(Icons.photo_library),
                    title: Text('Thư viện ảnh'),
                    onTap: () => Navigator.of(context).pop(ImageSource.gallery),
                  ),
                  ListTile(
                    leading: Icon(Icons.camera_alt),
                    title: Text('Máy ảnh'),
                    onTap: () => Navigator.of(context).pop(ImageSource.camera),
                  ),
                ],
              ),
            ),
      );

      if (source == null) return;

      final XFile? image = await picker.pickImage(
        source: source,
        imageQuality: 80,
      );

      if (image != null) {
        // Lấy thông tin file ảnh
        final File imageFile = File(image.path);
        final int fileSize = await imageFile.length();
        final String fileName = path.basename(image.path);

        setState(() {
          _qrImageFile = imageFile;
        });

        // Hiển thị thông báo thành công với thông tin chi tiết
        _showSuccessSnackBar(
          'Đã chọn ảnh mã QR thành công\nTên file: $fileName\nKích thước: ${(fileSize / 1024).toStringAsFixed(2)} KB',
        );
      }
    } catch (e) {
      _showErrorSnackBar('Không thể chọn ảnh: $e');
    }
  }

  Future<String?> _uploadQRImage() async {
    // Nếu không có file ảnh mới và đang chỉnh sửa tài khoản hiện có, giữ nguyên URL hiện tại
    if (_qrImageFile == null) {
      return _qrImageUrl;
    }

    try {
      LoadingDialog.show(context);

      // Tạo tên file dựa trên thời gian hiện tại và tên file gốc
      final fileName =
          'qr_${DateTime.now().millisecondsSinceEpoch}_${path.basename(_qrImageFile!.path)}';

      // Sử dụng StorageService để tải ảnh lên Imgur
      final storageService = StorageService();
      final imageUrl = await storageService.uploadFile(_qrImageFile!, fileName);

      if (mounted) LoadingDialog.hide(context);
      return imageUrl;
    } catch (e) {
      if (mounted) LoadingDialog.hide(context);
      _showErrorSnackBar('Không thể tải ảnh lên: $e');
      return null;
    }
  }

  Future<void> _saveBankAccount() async {
    if (!_formKey.currentState!.validate()) return;

    try {
      LoadingDialog.show(context);

      // Upload QR image if selected
      final uploadedImageUrl = await _uploadQRImage();

      // Kiểm tra nếu đang thêm mới và không có ảnh
      if (_editingAccount == null && uploadedImageUrl == null) {
        if (mounted) LoadingDialog.hide(context);
        _showErrorSnackBar('Vui lòng chọn ảnh mã QR');
        return;
      }

      // Kiểm tra nếu đang cập nhật và không có cả ảnh mới và ảnh cũ
      if (_editingAccount != null &&
          uploadedImageUrl == null &&
          _qrImageUrl == null) {
        if (mounted) LoadingDialog.hide(context);
        _showErrorSnackBar('Vui lòng chọn ảnh mã QR');
        return;
      }

      final qrUrl = uploadedImageUrl ?? _qrImageUrl ?? '';

      if (_editingAccount == null) {
        // Add new bank account
        final newAccount = await _bankAccountService.addBankAccount(
          bankName: _bankNameController.text,
          accountNumber: _accountNumberController.text,
          accountName: _accountNameController.text,
          qrImageUrl: qrUrl,
        );

        if (newAccount != null) {
          _showSuccessSnackBar('Thêm tài khoản ngân hàng thành công');
          _resetForm();
          _loadBankAccounts();
        } else {
          _showErrorSnackBar('Không thể thêm tài khoản ngân hàng');
        }
      } else {
        // Update existing bank account
        final updatedAccount = _editingAccount!.copyWith(
          bankName: _bankNameController.text,
          accountNumber: _accountNumberController.text,
          accountName: _accountNameController.text,
          qrImageUrl: qrUrl,
        );

        final success = await _bankAccountService.updateBankAccount(
          updatedAccount,
        );
        if (success) {
          _showSuccessSnackBar('Cập nhật tài khoản ngân hàng thành công');
          _resetForm();
          _loadBankAccounts();
        } else {
          _showErrorSnackBar('Không thể cập nhật tài khoản ngân hàng');
        }
      }

      if (mounted) LoadingDialog.hide(context);
    } catch (e) {
      if (mounted) LoadingDialog.hide(context);
      _showErrorSnackBar('Lỗi: $e');
    }
  }

  Future<void> _deleteBankAccount(BankAccount account) async {
    try {
      LoadingDialog.show(context);

      final success = await _bankAccountService.deleteBankAccount(account.id);

      if (mounted) LoadingDialog.hide(context);

      if (success) {
        _showSuccessSnackBar('Xóa tài khoản ngân hàng thành công');
        _loadBankAccounts();
      } else {
        _showErrorSnackBar('Không thể xóa tài khoản ngân hàng');
      }
    } catch (e) {
      if (mounted) LoadingDialog.hide(context);
      _showErrorSnackBar('Lỗi: $e');
    }
  }

  void _editBankAccount(BankAccount account) {
    setState(() {
      _editingAccount = account;
      _bankNameController.text = account.bankName;
      _accountNumberController.text = account.accountNumber;
      _accountNameController.text = account.accountName;
      _qrImageUrl = account.qrImageUrl;
      _qrImageFile = null;
    });
  }

  void _resetForm() {
    setState(() {
      _editingAccount = null;
      _bankNameController.clear();
      _accountNumberController.clear();
      _accountNameController.clear();
      _qrImageUrl = null;
      _qrImageFile = null;
    });
  }

  void _showSuccessSnackBar(String message) {
    final snackBar = SnackBar(
      elevation: 0,
      behavior: SnackBarBehavior.floating,
      backgroundColor: Colors.transparent,
      content: AwesomeSnackbarContent(
        title: 'Thành công',
        message: message,
        contentType: ContentType.success,
      ),
    );

    ScaffoldMessenger.of(context)
      ..hideCurrentSnackBar()
      ..showSnackBar(snackBar);
  }

  void _showErrorSnackBar(String message) {
    final snackBar = SnackBar(
      elevation: 0,
      behavior: SnackBarBehavior.floating,
      backgroundColor: Colors.transparent,
      content: AwesomeSnackbarContent(
        title: 'Lỗi',
        message: message,
        contentType: ContentType.failure,
      ),
    );

    ScaffoldMessenger.of(context)
      ..hideCurrentSnackBar()
      ..showSnackBar(snackBar);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Quản lý tài khoản ngân hàng'),
        actions: [
          IconButton(
            icon: Icon(Icons.refresh),
            onPressed: _loadBankAccounts,
            tooltip: 'Làm mới',
          ),
        ],
      ),
      body:
          _isLoading
              ? Center(child: CircularProgressIndicator())
              : SingleChildScrollView(
                padding: EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildBankAccountForm(),
                    SizedBox(height: 24),
                    Text(
                      'Danh sách tài khoản ngân hàng',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 8),
                    _bankAccounts.isEmpty
                        ? Center(
                          child: Padding(
                            padding: EdgeInsets.all(16),
                            child: Text(
                              'Chưa có tài khoản ngân hàng nào',
                              style: TextStyle(
                                color: Colors.grey,
                                fontSize: 16,
                              ),
                            ),
                          ),
                        )
                        : ListView.builder(
                          shrinkWrap: true,
                          physics: NeverScrollableScrollPhysics(),
                          itemCount: _bankAccounts.length,
                          itemBuilder: (context, index) {
                            final account = _bankAccounts[index];
                            return _buildBankAccountCard(account);
                          },
                        ),
                  ],
                ),
              ),
    );
  }

  Widget _buildBankAccountForm() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                _editingAccount == null
                    ? 'Thêm tài khoản ngân hàng mới'
                    : 'Cập nhật tài khoản ngân hàng',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 16),
              CustomTextFormField(
                controller: _bankNameController,
                label: 'Tên ngân hàng',
                prefixIcon: Icons.account_balance,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Vui lòng nhập tên ngân hàng';
                  }
                  return null;
                },
              ),
              SizedBox(height: 16),
              CustomTextFormField(
                controller: _accountNumberController,
                label: 'Số tài khoản',
                prefixIcon: Icons.credit_card,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Vui lòng nhập số tài khoản';
                  }
                  return null;
                },
              ),
              SizedBox(height: 16),
              CustomTextFormField(
                controller: _accountNameController,
                label: 'Tên chủ tài khoản',
                prefixIcon: Icons.person,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Vui lòng nhập tên chủ tài khoản';
                  }
                  return null;
                },
              ),
              SizedBox(height: 16),
              Text(
                'Ảnh mã QR',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 8),
              InkWell(
                onTap: _pickQRImage,
                child: Container(
                  height: 200,
                  width: double.infinity,
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child:
                      _qrImageFile != null
                          ? ClipRRect(
                            borderRadius: BorderRadius.circular(8),
                            child: Image.file(_qrImageFile!, fit: BoxFit.cover),
                          )
                          : _qrImageUrl != null
                          ? ClipRRect(
                            borderRadius: BorderRadius.circular(8),
                            child: Image.network(
                              _qrImageUrl!,
                              fit: BoxFit.cover,
                              loadingBuilder: (
                                context,
                                child,
                                loadingProgress,
                              ) {
                                if (loadingProgress == null) return child;
                                return Center(
                                  child: CircularProgressIndicator(
                                    value:
                                        loadingProgress.expectedTotalBytes !=
                                                null
                                            ? loadingProgress
                                                    .cumulativeBytesLoaded /
                                                loadingProgress
                                                    .expectedTotalBytes!
                                            : null,
                                  ),
                                );
                              },
                            ),
                          )
                          : Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.add_photo_alternate,
                                  size: 48,
                                  color: Colors.grey,
                                ),
                                SizedBox(height: 8),
                                Text(
                                  'Chọn ảnh mã QR',
                                  style: TextStyle(color: Colors.grey),
                                ),
                              ],
                            ),
                          ),
                ),
              ),
              SizedBox(height: 24),
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _saveBankAccount,
                      child: Text(
                        _editingAccount == null ? 'Thêm tài khoản' : 'Cập nhật',
                        style: TextStyle(fontSize: 16),
                      ),
                      style: ElevatedButton.styleFrom(
                        padding: EdgeInsets.symmetric(vertical: 12),
                      ),
                    ),
                  ),
                  if (_editingAccount != null) ...[
                    SizedBox(width: 16),
                    OutlinedButton(
                      onPressed: _resetForm,
                      child: Text('Hủy'),
                      style: OutlinedButton.styleFrom(
                        padding: EdgeInsets.symmetric(vertical: 12),
                      ),
                    ),
                  ],
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBankAccountCard(BankAccount account) {
    return Card(
      margin: EdgeInsets.only(bottom: 12),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    account.bankName,
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                ),
                Row(
                  children: [
                    IconButton(
                      icon: Icon(Icons.edit, color: Colors.blue),
                      onPressed: () => _editBankAccount(account),
                      tooltip: 'Chỉnh sửa',
                    ),
                    IconButton(
                      icon: Icon(Icons.delete, color: Colors.red),
                      onPressed: () => _showDeleteConfirmDialog(account),
                      tooltip: 'Xóa',
                    ),
                  ],
                ),
              ],
            ),
            Divider(),
            _buildInfoRow('Số tài khoản', account.accountNumber),
            _buildInfoRow('Chủ tài khoản', account.accountName),
            SizedBox(height: 8),
            if (account.qrImageUrl.isNotEmpty)
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Mã QR:',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.grey[700],
                    ),
                  ),
                  SizedBox(height: 8),
                  ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: Image.network(
                      account.qrImageUrl,
                      height: 150,
                      fit: BoxFit.contain,
                      loadingBuilder: (context, child, loadingProgress) {
                        if (loadingProgress == null) return child;
                        return Container(
                          height: 150,
                          width: 150,
                          child: Center(
                            child: CircularProgressIndicator(
                              value:
                                  loadingProgress.expectedTotalBytes != null
                                      ? loadingProgress.cumulativeBytesLoaded /
                                          loadingProgress.expectedTotalBytes!
                                      : null,
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '$label: ',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: Colors.grey[700],
            ),
          ),
          Expanded(child: Text(value, style: TextStyle(color: Colors.black87))),
        ],
      ),
    );
  }

  Future<void> _showDeleteConfirmDialog(BankAccount account) async {
    return showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('Xác nhận xóa'),
          content: SingleChildScrollView(
            child: ListBody(
              children: <Widget>[
                Text('Bạn có chắc chắn muốn xóa tài khoản ngân hàng này?'),
                SizedBox(height: 8),
                Text(
                  '${account.bankName} - ${account.accountNumber}',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
              ],
            ),
          ),
          actions: <Widget>[
            TextButton(
              child: Text('Hủy'),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
            TextButton(
              child: Text('Xóa', style: TextStyle(color: Colors.red)),
              onPressed: () {
                Navigator.of(context).pop();
                _deleteBankAccount(account);
              },
            ),
          ],
        );
      },
    );
  }
}
