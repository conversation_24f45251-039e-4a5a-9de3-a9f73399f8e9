import 'dart:io';
import 'package:http/http.dart' as http;
import 'dart:convert';

class StorageService {
  // Thay YOUR_CLIENT_ID bằng Client ID bạn nhận được từ Imgur
  static const String _clientId = '9c861b9c0039de5';
  static const String _uploadUrl = 'https://api.imgur.com/3/image';

  Future<String> uploadFile(File imageFile, String fileName) async {
    try {
      // Đọc file ảnh dưới dạng byte
      List<int> imageBytes = await imageFile.readAsBytes();

      // Tạo FormData để gửi file lên Imgur
      var request = http.MultipartRequest('POST', Uri.parse(_uploadUrl));
      request.headers['Authorization'] = 'Client-ID $_clientId';
      request.files.add(
        http.MultipartFile.fromBytes('image', imageBytes, filename: fileName),
      );

      // <PERSON><PERSON>i request và nhận response
      var response = await request.send();
      var responseBody = await response.stream.bytesToString();

      // Phân tích JSON response
      var jsonResponse = jsonDecode(responseBody);
      if (response.statusCode == 200 && jsonResponse['success'] == true) {
        // Lấy URL của ảnh từ response
        String imageUrl = jsonResponse['data']['link'];
        return imageUrl;
      } else {
        throw Exception(
          'Error uploading to Imgur: ${jsonResponse['data']['error'] ?? 'Unknown error'}',
        );
      }
    } catch (e) {
      throw Exception('Error uploading image: $e');
    }
  }
}
