// ignore_for_file: deprecated_member_use

import 'package:bike_rental_app/models/payment.dart';
import 'package:bike_rental_app/screens/payment/payment_detail_screen.dart';
import 'package:bike_rental_app/services/rental_service.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:bike_rental_app/widgets/common_widgets.dart';

class PaymentItem extends StatefulWidget {
  final Payment payment;
  final Function? onRefresh;

  const PaymentItem({super.key, required this.payment, this.onRefresh});

  @override
  _PaymentItemState createState() => _PaymentItemState();
}

class _PaymentItemState extends State<PaymentItem> {
  final RentalService _rentalService = RentalService();
  String rentalInfo = '';
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadRentalInfo();
  }

  Future<void> _loadRentalInfo() async {
    try {
      final rental = await _rentalService.getRentalById(
        widget.payment.rentalId,
      );
      setState(() {
        rentalInfo = 'Đơn thuê #${rental.id.substring(0, 8)}';
        isLoading = false;
      });
    } catch (e) {
      setState(() {
        rentalInfo = 'Không thể tải thông tin đơn thuê';
        isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Card(
      elevation: 3,
      margin: EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder:
                  (context) => PaymentDetailScreen(payment: widget.payment),
            ),
          );
        },
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Thanh toán #${widget.payment.id.substring(0, 8)}',
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    widget.payment.status == 'Completed'
                        ? '+${NumberFormat.currency(locale: 'vi_VN', symbol: 'đ').format(widget.payment.amount)}'
                        : NumberFormat.currency(
                          locale: 'vi_VN',
                          symbol: 'đ',
                        ).format(widget.payment.amount),
                    style: theme.textTheme.titleMedium?.copyWith(
                      color: _getStatusColor(widget.payment.status),
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              Divider(height: 24),
              isLoading
                  ? Center(
                    child: SizedBox(
                      height: 30,
                      child: AppLoadingIndicator(
                        color: theme.colorScheme.primary,
                        size: 20,
                        type: LoadingIndicatorType.circularProgress,
                      ),
                    ),
                  )
                  : _buildInfoRow('Đơn thuê', rentalInfo),
              _buildInfoRow('Phương thức', widget.payment.paymentMethod),
              _buildInfoRow(
                'Ngày thanh toán',
                DateFormat(
                  'dd/MM/yyyy HH:mm',
                ).format(widget.payment.paymentDate),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    final theme = Theme.of(context);
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Text(
            '$label: ',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant.withOpacity(0.5),
              fontWeight: FontWeight.w500,
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'Completed':
        return Colors.green;
      case 'Pending':
        return Colors.orange;
      case 'Failed':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }
}
