name: bike_rental_app
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.7.2

dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  firebase_core:
  google_sign_in:
  firebase_auth:
  cloud_firestore:
  flutter_otp_text_field:
  intl: ^0.20.2
  pdf: ^3.11.3
  printing: ^5.14.2
  image_picker: ^1.1.2
  path: ^1.9.1
  http: ^1.3.0
  uuid: ^4.5.1
  google_sign_in_web: ^0.12.4+4
  panara_dialogs:
  flutter_map:
  latlong2:
  iconly:
  loading_animation_widget:
  qr_code_scanner_plus:
  permission_handler:
  shared_preferences:
  provider:
  qr_flutter:
  lottie: ^3.1.0
  animate_do: ^4.2.0
  one_clock:
  flutter_local_notifications:
  timezone:
  awesome_snackbar_content:
  mailer: ^6.1.0
  flutter_dotenv: ^5.1.0
  url_launcher: ^6.2.5
  crystal_navigation_bar: ^1.0.4
  cached_network_image: ^3.4.1
  share_plus: ^10.1.4
  path_provider: ^2.1.5
  flutter_signature_pad: ^3.0.0
  firebase_storage:
  fl_chart: ^0.71.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0

flutter:
  uses-material-design: true
  assets:
    - .env
    - assets/images/google_logo.png
    - assets/images/login.jpg
    - assets/images/otp.webp
    - assets/images/reset_password.jpg
    - assets/images/signup.jpg
    - assets/images/default_avatar.jpg
    - assets/images/company_bg.jpg
    - assets/images/company_logo.png
    - assets/images/main_logo.png
    - assets/animations/
    - assets/images/wallets/
    - assets/fonts/

  fonts:
    - family: Roboto
      fonts:
        - asset: assets/fonts/Roboto-Regular.ttf
        - asset: assets/fonts/Roboto-Bold.ttf
          weight: 700
