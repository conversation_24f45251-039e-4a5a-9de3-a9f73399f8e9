import 'package:flutter/material.dart';
import 'package:bike_rental_app/screens/admin/bank_account_management_screen.dart';
import 'package:bike_rental_app/screens/admin/staff_management_screen.dart';
import 'package:bike_rental_app/utils/animation_helper.dart';
import 'package:bike_rental_app/utils/responsive_helper.dart';
import 'package:bike_rental_app/widgets/responsive_layout.dart';

class AdminDashboardScreen extends StatelessWidget {
  const AdminDashboardScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final isLandscape = ResponsiveHelper.isLandscape(context);
    final adaptivePadding = ResponsiveHelper.adaptivePadding(context);
    final titleFontSize = ResponsiveHelper.adaptiveFontSize(
      context,
      isLandscape ? 16 : 18,
    );

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Quản trị hệ thống',
          style: TextStyle(
            fontSize: ResponsiveHelper.adaptiveFontSize(context, 18),
          ),
        ),
      ),
      body: OrientationResponsiveLayout(
        portrait: _buildPortraitLayout(context, adaptivePadding, titleFontSize),
        landscape: _buildLandscapeLayout(
          context,
          adaptivePadding,
          titleFontSize,
        ),
      ),
    );
  }

  Widget _buildPortraitLayout(
    BuildContext context,
    EdgeInsets padding,
    double titleFontSize,
  ) {
    // Tối ưu hóa layout cho màn hình dọc
    return SingleChildScrollView(
      padding: padding,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          AnimationHelper.fadeInUp(
            child: Text(
              'QUẢN LÝ HỆ THỐNG',
              style: TextStyle(
                fontSize: titleFontSize,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).primaryColor,
              ),
            ),
          ),
          SizedBox(height: 16),
          _buildAdminGrid(context),
        ],
      ),
    );
  }

  Widget _buildLandscapeLayout(
    BuildContext context,
    EdgeInsets padding,
    double titleFontSize,
  ) {
    // Tối ưu hóa layout cho màn hình ngang
    return SingleChildScrollView(
      padding: EdgeInsets.symmetric(
        horizontal: padding.left,
        vertical: padding.top / 2, // Giảm padding dọc khi xoay ngang
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: AnimationHelper.fadeInUp(
                  child: Text(
                    'QUẢN LÝ HỆ THỐNG',
                    style: TextStyle(
                      fontSize: titleFontSize,
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).primaryColor,
                    ),
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 8), // Giảm khoảng cách khi xoay ngang
          _buildAdminGrid(context),
        ],
      ),
    );
  }

  Widget _buildAdminGrid(BuildContext context) {
    final isLandscape = ResponsiveHelper.isLandscape(context);
    final screenWidth = MediaQuery.of(context).size.width;

    // Tính toán số cột dựa trên kích thước màn hình và hướng
    int crossAxisCount = 2; // Mặc định là 2 cột
    double childAspectRatio = 1.2; // Tỷ lệ mặc định

    if (screenWidth > 600) {
      // Tablet hoặc màn hình lớn
      crossAxisCount = isLandscape ? 4 : 3;
      childAspectRatio = isLandscape ? 1.5 : 1.3;
    } else {
      // Điện thoại
      crossAxisCount = isLandscape ? 2 : 2;
      // Điều chỉnh tỷ lệ khi xoay ngang để tránh tràn
      childAspectRatio = isLandscape ? 2.5 : 1.2;
    }

    final List<Widget> adminCards = [
      _buildAdminCard(
        context,
        'Quản lý nhân viên',
        'Quản lý tài khoản nhân viên',
        Icons.people,
        Colors.blue,
        () => Navigator.push(
          context,
          MaterialPageRoute(builder: (_) => StaffManagementScreen()),
        ),
      ),
      _buildAdminCard(
        context,
        'Tài khoản ngân hàng',
        'Thông tin tài khoản ngân hàng',
        Icons.account_balance,
        Colors.green,
        () => Navigator.push(
          context,
          MaterialPageRoute(builder: (_) => BankAccountManagementScreen()),
        ),
      ),
      // Có thể thêm các tính năng quản trị khác ở đây
    ];

    return GridView.count(
      crossAxisCount: crossAxisCount,
      crossAxisSpacing: ResponsiveHelper.responsiveValue(
        context: context,
        mobile: isLandscape ? 8 : 12,
        tablet: isLandscape ? 12 : 16,
        desktop: 20,
      ),
      mainAxisSpacing: ResponsiveHelper.responsiveValue(
        context: context,
        mobile: isLandscape ? 8 : 12,
        tablet: isLandscape ? 12 : 16,
        desktop: 20,
      ),
      shrinkWrap: true,
      physics: NeverScrollableScrollPhysics(),
      childAspectRatio: childAspectRatio,
      children: adminCards,
    );
  }

  Widget _buildAdminCard(
    BuildContext context,
    String title,
    String subtitle,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    final isLandscape = ResponsiveHelper.isLandscape(context);
    final theme = Theme.of(context);

    // Tính toán kích thước icon và padding dựa trên kích thước và hướng màn hình
    final double iconSize = ResponsiveHelper.responsiveValue(
      context: context,
      mobile: isLandscape ? 24 : 32,
      tablet: isLandscape ? 28 : 36,
    );

    final double cardPadding = ResponsiveHelper.responsiveValue(
      context: context,
      mobile: isLandscape ? 4 : 10,
      tablet: isLandscape ? 6 : 12,
    );

    final double titleFontSize = ResponsiveHelper.adaptiveFontSize(
      context,
      isLandscape ? 12 : 14,
    );

    final double subtitleFontSize = ResponsiveHelper.adaptiveFontSize(
      context,
      isLandscape ? 9 : 11,
    );

    return AnimationHelper.fadeInUp(
      child: Card(
        elevation: 4,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(16),
          child: Padding(
            padding: EdgeInsets.all(cardPadding),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                AnimationHelper.bounce(
                  child: Icon(icon, size: iconSize, color: color),
                ),
                SizedBox(height: isLandscape ? 4 : 6),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: titleFontSize,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: isLandscape ? 1 : 3),
                Text(
                  subtitle,
                  style: TextStyle(
                    fontSize: subtitleFontSize,
                    color: theme.textTheme.bodySmall?.color,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
