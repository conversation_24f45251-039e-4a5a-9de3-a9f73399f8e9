// ignore_for_file: deprecated_member_use

import 'package:awesome_snackbar_content/awesome_snackbar_content.dart';
import 'package:bike_rental_app/models/rental.dart';
import 'package:bike_rental_app/screens/payment/create_payment_screen.dart';
import 'package:bike_rental_app/screens/rental/map/bike_location_map_screen.dart';
import 'package:bike_rental_app/screens/rental/rental_detail_screen.dart';
import 'package:bike_rental_app/services/bike_service.dart';
import 'package:bike_rental_app/services/rental_service.dart';
import 'package:bike_rental_app/services/user_service.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class RentalItem extends StatefulWidget {
  final Rental rental;
  final Function? onRefresh;

  const RentalItem({super.key, required this.rental, this.onRefresh});

  @override
  State<RentalItem> createState() => _RentalItemState();
}

class _RentalItemState extends State<RentalItem> {
  final UserService _userService = UserService();
  final BikeService _bikeService = BikeService();
  final RentalService _rentalService = RentalService();

  String _userName = '';
  String _bikeName = '';
  String _licensePlate = '';

  @override
  void initState() {
    super.initState();
    _loadDetails();
  }

  Future<void> _loadDetails() async {
    try {
      // Lấy thông tin người dùng
      final user = await _userService.getUserById(widget.rental.userId);
      // Lấy thông tin xe máy
      final bike = await _bikeService.getBikeById(widget.rental.bikeId);

      if (mounted) {
        setState(() {
          _userName = user?.name ?? 'Không xác định';
          _bikeName = bike.name;
          _licensePlate = bike.licensePlate;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _userName = 'Lỗi tải dữ liệu';
          _bikeName = 'Lỗi tải dữ liệu';
          _licensePlate = 'Lỗi tải dữ liệu';
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Card(
      elevation: 2,
      margin: EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      clipBehavior: Clip.antiAlias,
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [theme.cardColor, theme.cardColor.withOpacity(0.9)],
          ),
          border: Border.all(
            color: theme.dividerColor.withOpacity(0.2),
            width: 1.0,
          ),
        ),
        child: InkWell(
          onTap: () => _navigateToDetails(context),
          child: Padding(
            padding: EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header with ID and Status
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Đơn thuê #${widget.rental.id.substring(0, 8)}',
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: _getStatusColor(
                          widget.rental.status,
                        ).withOpacity(0.2),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        widget.rental.status,
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: _getStatusColor(widget.rental.status),
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 12),

                // Main information section with glass morphism effect
                Container(
                  margin: EdgeInsets.symmetric(vertical: 8),
                  padding: EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.surface.withOpacity(0.3),
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: theme.shadowColor.withOpacity(0.1),
                        blurRadius: 10,
                        spreadRadius: 0,
                      ),
                    ],
                    border: Border.all(
                      color: theme.dividerColor.withOpacity(0.2),
                      width: 0.5,
                    ),
                  ),
                  child: Column(
                    children: [
                      // Top row with bike info and user
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Left column - Bike info with icon
                          Expanded(
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Icon(
                                  Icons.motorcycle,
                                  size: 18,
                                  color: theme.primaryColor,
                                ),
                                SizedBox(width: 8),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        _bikeName,
                                        style: theme.textTheme.bodyMedium
                                            ?.copyWith(
                                              fontWeight: FontWeight.bold,
                                            ),
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                      Text(
                                        _licensePlate,
                                        style: theme.textTheme.bodySmall
                                            ?.copyWith(
                                              color: theme
                                                  .textTheme
                                                  .bodySmall
                                                  ?.color
                                                  ?.withOpacity(0.7),
                                            ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                          SizedBox(width: 16),
                          // Right column - User info with icon
                          Expanded(
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Icon(
                                  Icons.person,
                                  size: 18,
                                  color: theme.primaryColor,
                                ),
                                SizedBox(width: 8),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        _userName,
                                        style: theme.textTheme.bodyMedium
                                            ?.copyWith(
                                              fontWeight: FontWeight.bold,
                                            ),
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                      Text(
                                        'Người thuê',
                                        style: theme.textTheme.bodySmall
                                            ?.copyWith(
                                              color: theme
                                                  .textTheme
                                                  .bodySmall
                                                  ?.color
                                                  ?.withOpacity(0.7),
                                            ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),

                      Divider(height: 24, thickness: 0.5),

                      // Bottom row with dates and price
                      Row(
                        children: [
                          // Left column - Rental dates with icon
                          Expanded(
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Icon(
                                  Icons.date_range,
                                  size: 18,
                                  color: theme.primaryColor,
                                ),
                                SizedBox(width: 8),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        DateFormat(
                                          'dd/MM/yyyy',
                                        ).format(widget.rental.startTime),
                                        style: theme.textTheme.bodyMedium
                                            ?.copyWith(
                                              fontWeight: FontWeight.bold,
                                            ),
                                      ),
                                      Text(
                                        'đến ${DateFormat('dd/MM/yyyy').format(widget.rental.endTime)}',
                                        style: theme.textTheme.bodySmall
                                            ?.copyWith(
                                              color: theme
                                                  .textTheme
                                                  .bodySmall
                                                  ?.color
                                                  ?.withOpacity(0.7),
                                            ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),

                          // Right column - Price with emphasis
                          Container(
                            padding: EdgeInsets.symmetric(
                              horizontal: 12,
                              vertical: 6,
                            ),
                            decoration: BoxDecoration(
                              color: theme.primaryColor.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(16),
                            ),
                            child: Row(
                              children: [
                                Icon(
                                  Icons.monetization_on,
                                  size: 16,
                                  color: theme.primaryColor,
                                ),
                                SizedBox(width: 4),
                                Text(
                                  NumberFormat.currency(
                                    locale: 'vi_VN',
                                    symbol: 'đ',
                                    decimalDigits: 0,
                                  ).format(widget.rental.totalAmount),
                                  style: theme.textTheme.bodyMedium?.copyWith(
                                    fontWeight: FontWeight.bold,
                                    color: theme.primaryColor,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),

                // Action buttons section
                if (widget.rental.status == 'Ongoing' ||
                    widget.rental.status == 'Expired')
                  Padding(
                    padding: EdgeInsets.only(top: 12),
                    child: LayoutBuilder(
                      builder: (context, constraints) {
                        // Check if we need to show the cancel button
                        bool showCancelButton =
                            widget.rental.status == 'Ongoing' &&
                            _canCancelRental();

                        // If screen is narrow, use a column layout for buttons
                        if (constraints.maxWidth < 320) {
                          return Column(
                            crossAxisAlignment: CrossAxisAlignment.stretch,
                            children: [
                              // First row with Location and Payment buttons
                              Row(
                                children: [
                                  // Location button
                                  Expanded(
                                    child: ElevatedButton.icon(
                                      icon: Icon(Icons.location_on, size: 16),
                                      label: Text(
                                        'Định vị',
                                        style: TextStyle(fontSize: 13),
                                      ),
                                      style: ElevatedButton.styleFrom(
                                        backgroundColor:
                                            theme.colorScheme.primary,
                                        foregroundColor:
                                            theme.colorScheme.onPrimary,
                                        padding: EdgeInsets.symmetric(
                                          vertical: 8,
                                        ),
                                      ),
                                      onPressed:
                                          () => Navigator.push(
                                            context,
                                            MaterialPageRoute(
                                              builder:
                                                  (context) =>
                                                      BikeLocationMapScreen(
                                                        bikeId:
                                                            widget
                                                                .rental
                                                                .bikeId,
                                                      ),
                                            ),
                                          ),
                                    ),
                                  ),
                                  SizedBox(width: 8),
                                  // Payment button
                                  Expanded(
                                    child: ElevatedButton.icon(
                                      icon: Icon(Icons.payment, size: 16),
                                      label: Text(
                                        'Thanh toán',
                                        style: TextStyle(fontSize: 13),
                                      ),
                                      style: ElevatedButton.styleFrom(
                                        backgroundColor: Colors.green,
                                        foregroundColor: Colors.white,
                                        padding: EdgeInsets.symmetric(
                                          vertical: 8,
                                        ),
                                      ),
                                      onPressed:
                                          () => Navigator.push(
                                            context,
                                            MaterialPageRoute(
                                              builder:
                                                  (_) => CreatePaymentScreen(
                                                    rentalId: widget.rental.id,
                                                  ),
                                            ),
                                          ).then((_) {
                                            if (widget.onRefresh != null) {
                                              widget.onRefresh!();
                                            }
                                          }),
                                    ),
                                  ),
                                ],
                              ),

                              // Cancel button in second row if needed
                              if (showCancelButton)
                                Padding(
                                  padding: EdgeInsets.only(top: 8),
                                  child: ElevatedButton.icon(
                                    icon: Icon(Icons.cancel, size: 16),
                                    label: Text(
                                      'Hủy đơn thuê',
                                      style: TextStyle(fontSize: 13),
                                    ),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: theme.colorScheme.error,
                                      foregroundColor:
                                          theme.colorScheme.onError,
                                      padding: EdgeInsets.symmetric(
                                        vertical: 8,
                                      ),
                                    ),
                                    onPressed: _cancelRental,
                                  ),
                                ),
                            ],
                          );
                        } else {
                          // For wider screens, use a single row with all buttons
                          return Wrap(
                            spacing: 8,
                            runSpacing: 8,
                            alignment: WrapAlignment.end,
                            children: [
                              // Location button
                              ElevatedButton.icon(
                                icon: Icon(Icons.location_on, size: 16),
                                label: Text('Định vị'),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: theme.colorScheme.primary,
                                  foregroundColor: theme.colorScheme.onPrimary,
                                  padding: EdgeInsets.symmetric(
                                    horizontal: 10,
                                    vertical: 8,
                                  ),
                                ),
                                onPressed:
                                    () => Navigator.push(
                                      context,
                                      MaterialPageRoute(
                                        builder:
                                            (context) => BikeLocationMapScreen(
                                              bikeId: widget.rental.bikeId,
                                            ),
                                      ),
                                    ),
                              ),

                              // Payment button
                              ElevatedButton.icon(
                                icon: Icon(Icons.payment, size: 16),
                                label: Text('Thanh toán'),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.green,
                                  foregroundColor: Colors.white,
                                  padding: EdgeInsets.symmetric(
                                    horizontal: 10,
                                    vertical: 8,
                                  ),
                                ),
                                onPressed:
                                    () => Navigator.push(
                                      context,
                                      MaterialPageRoute(
                                        builder:
                                            (_) => CreatePaymentScreen(
                                              rentalId: widget.rental.id,
                                            ),
                                      ),
                                    ).then((_) {
                                      if (widget.onRefresh != null) {
                                        widget.onRefresh!();
                                      }
                                    }),
                              ),

                              // Cancel button (only for eligible rentals)
                              if (showCancelButton)
                                ElevatedButton.icon(
                                  icon: Icon(Icons.cancel, size: 16),
                                  label: Text('Hủy đơn'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: theme.colorScheme.error,
                                    foregroundColor: theme.colorScheme.onError,
                                    padding: EdgeInsets.symmetric(
                                      horizontal: 10,
                                      vertical: 8,
                                    ),
                                  ),
                                  onPressed: _cancelRental,
                                ),
                            ],
                          );
                        }
                      },
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _navigateToDetails(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => RentalDetailScreen(rentalId: widget.rental.id),
      ),
    );
  }

  // Kiểm tra xem đơn thuê có thể hủy không (trong vòng 30 phút từ khi tạo)
  bool _canCancelRental() {
    if (widget.rental.status != 'Ongoing') {
      return false;
    }

    final now = DateTime.now();
    final difference = now.difference(widget.rental.createdAt);
    return difference.inMinutes < 30;
  }

  // Hủy đơn thuê
  Future<void> _cancelRental() async {
    try {
      // Hiển thị hộp thoại xác nhận
      bool confirm =
          await showDialog(
            context: context,
            builder:
                (context) => AlertDialog(
                  title: Text('Xác nhận hủy đơn'),
                  content: Text(
                    'Bạn có chắc chắn muốn hủy đơn thuê này không?',
                  ),
                  actions: [
                    TextButton(
                      onPressed: () => Navigator.of(context).pop(false),
                      child: Text('Hủy'),
                    ),
                    TextButton(
                      onPressed: () => Navigator.of(context).pop(true),
                      child: Text('Xác nhận'),
                    ),
                  ],
                ),
          ) ??
          false;

      if (!confirm) return;

      // Hiển thị loading
      setState(() {});

      // Gọi API hủy đơn thuê
      await _rentalService.cancelRental(widget.rental.id);

      // Gọi hàm refresh nếu có
      if (widget.onRefresh != null) {
        widget.onRefresh!();
      }

      // Hiển thị thông báo thành công
      if (mounted) {
        final snackBar = SnackBar(
          elevation: 0,
          backgroundColor: Colors.transparent,
          behavior: SnackBarBehavior.floating,
          content: AwesomeSnackbarContent(
            contentType: ContentType.success,
            title: 'Thành công',
            message: 'Đơn thuê đã được hủy thành công',
          ),
        );
        ScaffoldMessenger.of(context)
          ..hideCurrentSnackBar()
          ..showSnackBar(snackBar);
      }
    } catch (e) {
      // Tắt loading
      setState(() {});

      // Hiển thị thông báo lỗi
      if (mounted) {
        final snackBar = SnackBar(
          elevation: 0,
          backgroundColor: Colors.transparent,
          behavior: SnackBarBehavior.floating,
          content: AwesomeSnackbarContent(
            contentType: ContentType.failure,
            title: 'Lỗi',
            message: 'Không thể hủy đơn thuê: ${e.toString()}',
          ),
        );
        ScaffoldMessenger.of(context)
          ..hideCurrentSnackBar()
          ..showSnackBar(snackBar);
      }
    }
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'Ongoing':
        return Colors.orange;
      case 'Completed':
        return Colors.green;
      case 'Expired':
        return Colors.red;
      case 'Cancelled':
        return Colors.grey;
      default:
        return Colors.grey;
    }
  }
}
