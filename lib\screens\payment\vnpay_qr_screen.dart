import 'dart:async';
import 'package:flutter/material.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'package:intl/intl.dart';
import 'package:awesome_snackbar_content/awesome_snackbar_content.dart';
import 'package:bike_rental_app/models/payment.dart';
import 'package:bike_rental_app/services/payment_service.dart';
import 'package:bike_rental_app/screens/payment/payment_receipt_screen.dart';
import 'package:bike_rental_app/utils/loading_dialog.dart';

class VNPayQRScreen extends StatefulWidget {
  final Payment payment;
  final Map<String, dynamic> qrData;

  const VNPayQRScreen({Key? key, required this.payment, required this.qrData})
    : super(key: key);

  @override
  _VNPayQRScreenState createState() => _VNPayQRScreenState();
}

class _VNPayQRScreenState extends State<VNPayQRScreen> {
  late Timer _timer;
  late DateTime _expiryTime;
  Duration _timeLeft = Duration.zero;
  bool _isCheckingStatus = false;
  int _checkCount = 0;

  @override
  void initState() {
    super.initState();
    _expiryTime = widget.qrData['expiryTime'] as DateTime;
    _updateTimeLeft();

    // Start timer to update countdown and check payment status
    _timer = Timer.periodic(Duration(seconds: 1), (timer) {
      _updateTimeLeft();

      // Check payment status every 5 seconds
      if (_checkCount % 5 == 0 && !_isCheckingStatus) {
        _checkPaymentStatus();
      }
      _checkCount++;
    });
  }

  @override
  void dispose() {
    _timer.cancel();
    super.dispose();
  }

  void _updateTimeLeft() {
    final now = DateTime.now();
    if (_expiryTime.isAfter(now)) {
      setState(() {
        _timeLeft = _expiryTime.difference(now);
      });
    } else {
      setState(() {
        _timeLeft = Duration.zero;
      });
      _timer.cancel();
    }
  }

  Future<void> _checkPaymentStatus() async {
    if (_isCheckingStatus) return;

    setState(() {
      _isCheckingStatus = true;
    });

    try {
      // In a real app, this would check the payment status with the server
      final paymentService = PaymentService();
      final payment = await paymentService.getPaymentById(widget.payment.id);

      if (payment.status == PaymentStatusConstants.completed) {
        _timer.cancel();

        // Show success message
        final snackBar = SnackBar(
          elevation: 0,
          behavior: SnackBarBehavior.floating,
          backgroundColor: Colors.transparent,
          content: AwesomeSnackbarContent(
            title: 'Thanh toán thành công',
            message: 'Thanh toán của bạn đã được xử lý thành công',
            contentType: ContentType.success,
          ),
        );

        ScaffoldMessenger.of(context)
          ..hideCurrentSnackBar()
          ..showSnackBar(snackBar);

        // Navigate to receipt screen
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(
            builder: (context) => PaymentReceiptScreen(payment: payment),
          ),
        );
      }
    } catch (e) {
      print('Error checking payment status: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isCheckingStatus = false;
        });
      }
    }
  }

  Future<void> _manualCheckStatus() async {
    LoadingDialog.show(context);

    try {
      // Simulate VNPay callback
      final paymentService = PaymentService();
      final updatedPayment = await paymentService.processVNPayCallback(
        paymentId: widget.payment.id,
        forceSuccess: true, // For demo purposes
      );

      if (mounted) LoadingDialog.hide(context);

      if (updatedPayment.status == PaymentStatusConstants.completed) {
        _timer.cancel();

        // Show success message
        final snackBar = SnackBar(
          elevation: 0,
          behavior: SnackBarBehavior.floating,
          backgroundColor: Colors.transparent,
          content: AwesomeSnackbarContent(
            title: 'Thanh toán thành công',
            message: 'Thanh toán của bạn đã được xử lý thành công',
            contentType: ContentType.success,
          ),
        );

        ScaffoldMessenger.of(context)
          ..hideCurrentSnackBar()
          ..showSnackBar(snackBar);

        // Navigate to receipt screen
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(
            builder: (context) => PaymentReceiptScreen(payment: updatedPayment),
          ),
        );
      }
    } catch (e) {
      if (mounted) LoadingDialog.hide(context);

      final snackBar = SnackBar(
        elevation: 0,
        behavior: SnackBarBehavior.floating,
        backgroundColor: Colors.transparent,
        content: AwesomeSnackbarContent(
          title: 'Lỗi xử lý thanh toán',
          message: e.toString(),
          contentType: ContentType.failure,
        ),
      );

      ScaffoldMessenger.of(context)
        ..hideCurrentSnackBar()
        ..showSnackBar(snackBar);
    }
  }

  String _formatTimeLeft() {
    final minutes = _timeLeft.inMinutes
        .remainder(60)
        .toString()
        .padLeft(2, '0');
    final seconds = _timeLeft.inSeconds
        .remainder(60)
        .toString()
        .padLeft(2, '0');
    return '$minutes:$seconds';
  }

  @override
  Widget build(BuildContext context) {
    final formatCurrency = NumberFormat.currency(locale: 'vi_VN', symbol: '₫');
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(title: Text('Thanh toán VNPay')),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // QR Code section
            Card(
              elevation: 4,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              child: Padding(
                padding: EdgeInsets.all(16),
                child: Column(
                  children: [
                    Text(
                      'Quét mã QR để thanh toán',
                      style: theme.textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 8),
                    Text(
                      'Sử dụng ứng dụng ngân hàng hoặc ví điện tử để quét',
                      textAlign: TextAlign.center,
                      style: theme.textTheme.bodyMedium,
                    ),
                    SizedBox(height: 24),

                    // QR Code
                    Container(
                      padding: EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(color: Colors.grey.shade300),
                      ),
                      child: QrImageView(
                        data: widget.qrData['qrContent'] as String,
                        version: QrVersions.auto,
                        size: 200,
                        backgroundColor: Colors.white,
                      ),
                    ),

                    SizedBox(height: 16),

                    // Timer
                    _timeLeft.inSeconds > 0
                        ? Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(Icons.timer, color: Colors.orange),
                            SizedBox(width: 8),
                            Text(
                              'Mã QR hết hạn sau: ${_formatTimeLeft()}',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.orange,
                              ),
                            ),
                          ],
                        )
                        : Text(
                          'Mã QR đã hết hạn',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.red,
                          ),
                        ),
                  ],
                ),
              ),
            ),

            SizedBox(height: 16),

            // Payment details
            Card(
              elevation: 2,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              child: Padding(
                padding: EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Chi tiết thanh toán',
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Divider(),
                    _buildInfoRow(
                      'Mã đơn thuê',
                      '#${widget.payment.rentalId.substring(0, 8)}',
                    ),
                    _buildInfoRow(
                      'Số tiền',
                      formatCurrency.format(widget.payment.amount),
                    ),
                    _buildInfoRow('Phương thức', widget.payment.paymentMethod),
                    _buildInfoRow(
                      'Đơn vị thanh toán',
                      widget.qrData['merchantName'] as String,
                    ),
                  ],
                ),
              ),
            ),

            SizedBox(height: 24),

            // Action buttons
            Row(
              children: [
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () {
                      Navigator.pop(context);
                    },
                    icon: Icon(Icons.cancel),
                    label: Text('Hủy thanh toán'),
                    style: OutlinedButton.styleFrom(
                      padding: EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
                SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed:
                        _timeLeft.inSeconds > 0 ? _manualCheckStatus : null,
                    icon: Icon(Icons.check_circle),
                    label: Text('Đã thanh toán'),
                    style: ElevatedButton.styleFrom(
                      padding: EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
              ],
            ),

            SizedBox(height: 16),

            // Instructions
            Card(
              elevation: 1,
              color: Colors.blue.shade50,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              child: Padding(
                padding: EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Hướng dẫn thanh toán',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    SizedBox(height: 8),
                    _buildInstructionStep(
                      '1',
                      'Mở ứng dụng ngân hàng hoặc ví điện tử hỗ trợ VNPay',
                    ),
                    _buildInstructionStep('2', 'Chọn chức năng quét mã QR'),
                    _buildInstructionStep('3', 'Quét mã QR hiển thị ở trên'),
                    _buildInstructionStep(
                      '4',
                      'Xác nhận thông tin và hoàn tất thanh toán',
                    ),
                    _buildInstructionStep(
                      '5',
                      'Nhấn "Đã thanh toán" sau khi hoàn tất',
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text('$label:', style: TextStyle(color: Colors.grey[600])),
          Text(value, style: TextStyle(fontWeight: FontWeight.bold)),
        ],
      ),
    );
  }

  Widget _buildInstructionStep(String number, String text) {
    return Padding(
      padding: EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              color: Colors.blue,
              shape: BoxShape.circle,
            ),
            child: Center(
              child: Text(
                number,
                style: TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          SizedBox(width: 12),
          Expanded(child: Text(text)),
        ],
      ),
    );
  }
}
