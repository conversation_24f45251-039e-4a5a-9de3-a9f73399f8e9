import 'package:awesome_snackbar_content/awesome_snackbar_content.dart';
import 'package:bike_rental_app/models/bike.dart';
import 'package:bike_rental_app/models/brand.dart';
import 'package:bike_rental_app/models/rental.dart';
import 'package:bike_rental_app/services/bike_service.dart';
import 'package:bike_rental_app/services/rental_service.dart';
import 'package:bike_rental_app/services/user_service.dart';
import 'package:bike_rental_app/widgets/custom_text_form_field.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:loading_animation_widget/loading_animation_widget.dart';

class CreateRentalScreen extends StatefulWidget {
  final String bikeId;
  final String userId;

  const CreateRentalScreen({
    super.key,
    required this.bikeId,
    required this.userId,
  });

  @override
  _CreateRentalScreenState createState() => _CreateRentalScreenState();
}

class _CreateRentalScreenState extends State<CreateRentalScreen> {
  final RentalService _rentalService = RentalService();
  final BikeService _bikeService = BikeService();
  final UserService _userService = UserService();
  final _formKey = GlobalKey<FormState>();

  Timestamp? startTime;
  Timestamp? endTime;
  int quantity = 1;
  Timestamp? createdAt = Timestamp.now();
  double totalAmount = 0;
  Bike? bike;
  Brand? bikeBrand;
  String userName = '';
  bool _isLoading = true;
  bool _isSubmitting = false;
  final currencyFormat = NumberFormat.currency(locale: 'vi_VN', symbol: '₫');

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    try {
      final loadedBike = await _bikeService.getBikeById(widget.bikeId);
      final user = await _userService.getUserById(widget.userId);

      Brand? brand;
      try {
        // Lấy thông tin Brand từ Firestore
        brand = await FirebaseFirestore.instance
            .collection('brands')
            .doc(loadedBike.brandId)
            .get()
            .then((doc) {
              if (doc.exists) {
                final data = doc.data() as Map<String, dynamic>;
                return Brand(id: doc.id, name: data['name']);
              }
              return null;
            });
      } catch (e) {
        print('Error fetching brand: $e');
      }

      setState(() {
        bike = loadedBike;
        bikeBrand = brand;
        userName = user?.name ?? 'Người dùng';
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      final snackBar = SnackBar(
        elevation: 0,
        backgroundColor: Colors.transparent,
        behavior: SnackBarBehavior.floating,
        content: AwesomeSnackbarContent(
          contentType: ContentType.failure,
          title: 'Lỗi',
          message: 'Không thể tải thông tin',
        ),
      );
      ScaffoldMessenger.of(context)
        ..hideCurrentSnackBar()
        ..showSnackBar(snackBar);
    }
  }

  void _calculateTotalAmount() {
    if (startTime != null && endTime != null && bike != null) {
      final days = endTime!.toDate().difference(startTime!.toDate()).inDays;
      if (days > 0) {
        setState(() {
          totalAmount = quantity * bike!.price * days;
        });
      } else {
        final snackBar = SnackBar(
          elevation: 0,
          backgroundColor: Colors.transparent,
          behavior: SnackBarBehavior.floating,
          content: AwesomeSnackbarContent(
            contentType: ContentType.warning,
            title: 'Thông báo',
            message: 'Ngày kết thúc phải sau ngày bắt đầu',
          ),
        );
        ScaffoldMessenger.of(context)
          ..hideCurrentSnackBar()
          ..showSnackBar(snackBar);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Scaffold(
      appBar: AppBar(
        bottom: PreferredSize(
          preferredSize: Size.fromHeight(1.0),
          child: Container(
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(
                  color: Theme.of(context).primaryColor.withOpacity(0.2),
                  width: 1.0,
                ),
              ),
            ),
          ),
        ),
        title: Text(
          'Tạo đơn thuê mới',
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: theme.appBarTheme.backgroundColor,
        elevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.appBarTheme.foregroundColor,
          ),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body:
          _isLoading
              ? Center(
                child: LoadingAnimationWidget.fourRotatingDots(
                  color: theme.primaryColor,
                  size: 30,
                ),
              )
              : bike == null
              ? Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.error_outline, size: 60, color: Colors.red),
                    SizedBox(height: 16),
                    Text(
                      'Không tìm thấy thông tin xe máy',
                      style: theme.textTheme.bodyLarge,
                    ),
                    SizedBox(height: 24),
                    ElevatedButton(
                      onPressed: () => Navigator.pop(context),
                      child: Text('Quay lại'),
                    ),
                  ],
                ),
              )
              : Container(
                decoration: BoxDecoration(color: theme.scaffoldBackgroundColor),
                child: SafeArea(
                  child: SingleChildScrollView(
                    child: Padding(
                      padding: EdgeInsets.all(16.0),
                      child: Column(
                        children: [
                          // Bike and User Info Card
                          Card(
                            elevation: 4,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(16),
                            ),
                            child: Padding(
                              padding: EdgeInsets.all(16.0),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    children: [
                                      Icon(
                                        Icons.motorcycle,
                                        color: theme.colorScheme.primary,
                                        size: 24,
                                      ),
                                      SizedBox(width: 8),
                                      Text(
                                        'Thông tin xe máy',
                                        style: theme.textTheme.titleLarge
                                            ?.copyWith(
                                              fontWeight: FontWeight.bold,
                                            ),
                                      ),
                                    ],
                                  ),
                                  Divider(),
                                  _infoRow('Tên xe:', bike!.name),
                                  _infoRow('Hãng xe:', bikeBrand!.name),
                                  _infoRow('Loại xe:', bike!.type),
                                  _infoRow(
                                    'Giá thuê/ngày:',
                                    currencyFormat.format(bike!.price),
                                  ),
                                  _infoRow(
                                    'Số lượng có sẵn:',
                                    bike!.quantity.toString(),
                                  ),
                                  SizedBox(height: 16),
                                  Row(
                                    children: [
                                      Icon(
                                        Icons.person,
                                        color: theme.colorScheme.primary,
                                        size: 24,
                                      ),
                                      SizedBox(width: 8),
                                      Text(
                                        'Thông tin người thuê',
                                        style: Theme.of(
                                          context,
                                        ).textTheme.titleLarge?.copyWith(
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ],
                                  ),
                                  Divider(),
                                  _infoRow('Tên người thuê:', userName),
                                ],
                              ),
                            ),
                          ),

                          // Rental Form Card
                          Card(
                            elevation: 4,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(16),
                            ),
                            child: Padding(
                              padding: EdgeInsets.all(16.0),
                              child: Form(
                                key: _formKey,
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Row(
                                      children: [
                                        Icon(
                                          Icons.edit_document,
                                          color:
                                              Theme.of(
                                                context,
                                              ).colorScheme.primary,
                                          size: 24,
                                        ),
                                        SizedBox(width: 8),
                                        Text(
                                          'Chi tiết đơn thuê',
                                          style: Theme.of(
                                            context,
                                          ).textTheme.titleLarge?.copyWith(
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ],
                                    ),
                                    Divider(),
                                    SizedBox(height: 8),

                                    // Quantity Field
                                    CustomTextFormField(
                                      label: 'Số lượng',
                                      prefixIcon: Icons.numbers,
                                      controller: null,
                                      keyboardType: TextInputType.number,
                                      initialValue: '1',
                                      onChanged: (val) {
                                        setState(() {
                                          quantity = int.tryParse(val) ?? 1;
                                          _calculateTotalAmount();
                                        });
                                      },
                                      validator: (val) {
                                        int? q = int.tryParse(val ?? '');
                                        if (q == null || q <= 0) {
                                          return 'Số lượng phải lớn hơn 0';
                                        }
                                        if (q > bike!.quantity) {
                                          return 'Số lượng không được vượt quá ${bike!.quantity}';
                                        }
                                        return null;
                                      },
                                    ),
                                    SizedBox(height: 16),

                                    // Date Selection
                                    Row(
                                      children: [
                                        Expanded(
                                          child: _datePickerField(
                                            label: 'Ngày bắt đầu',
                                            value: startTime?.toDate(),
                                            onTap: () async {
                                              final DateTime? picked =
                                                  await showDatePicker(
                                                    context: context,
                                                    initialDate: DateTime.now(),
                                                    firstDate: DateTime.now(),
                                                    lastDate: DateTime(2100),
                                                  );
                                              if (picked != null) {
                                                setState(() {
                                                  startTime =
                                                      Timestamp.fromDate(
                                                        picked,
                                                      );
                                                  _calculateTotalAmount();
                                                });
                                              }
                                            },
                                          ),
                                        ),
                                        SizedBox(width: 16),
                                        Expanded(
                                          child: _datePickerField(
                                            label: 'Ngày kết thúc',
                                            value: endTime?.toDate(),
                                            onTap: () async {
                                              final DateTime? picked =
                                                  await showDatePicker(
                                                    context: context,
                                                    initialDate:
                                                        startTime?.toDate() ??
                                                        DateTime.now().add(
                                                          Duration(days: 1),
                                                        ),
                                                    firstDate:
                                                        startTime?.toDate() ??
                                                        DateTime.now(),
                                                    lastDate: DateTime(2100),
                                                  );
                                              if (picked != null) {
                                                setState(() {
                                                  endTime = Timestamp.fromDate(
                                                    picked,
                                                  );
                                                  _calculateTotalAmount();
                                                });
                                              }
                                            },
                                          ),
                                        ),
                                      ],
                                    ),

                                    SizedBox(height: 24),

                                    // Total Amount Display
                                    Container(
                                      padding: EdgeInsets.all(16),
                                      decoration: BoxDecoration(
                                        color: theme.colorScheme.primary
                                            .withOpacity(0.1),
                                        borderRadius: BorderRadius.circular(10),
                                        border: Border.all(
                                          color: theme.colorScheme.primary
                                              .withOpacity(0.1),
                                        ),
                                      ),
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          Text(
                                            'Tạm tính:',
                                            style: theme.textTheme.titleLarge,
                                          ),
                                          Text(
                                            currencyFormat.format(totalAmount),
                                            style: theme.textTheme.titleLarge
                                                ?.copyWith(
                                                  fontWeight: FontWeight.bold,
                                                  color:
                                                      Theme.of(
                                                        context,
                                                      ).primaryColor,
                                                ),
                                          ),
                                        ],
                                      ),
                                    ),

                                    SizedBox(height: 24),

                                    // Submit Button
                                    SizedBox(
                                      width: double.infinity,
                                      child: ElevatedButton(
                                        style: ElevatedButton.styleFrom(
                                          backgroundColor:
                                              Theme.of(
                                                context,
                                              ).colorScheme.primary,
                                          padding: EdgeInsets.symmetric(
                                            vertical: 16,
                                          ),
                                          shape: RoundedRectangleBorder(
                                            borderRadius: BorderRadius.circular(
                                              10,
                                            ),
                                          ),
                                        ),
                                        onPressed:
                                            _isSubmitting
                                                ? null
                                                : () async {
                                                  if (_formKey.currentState!
                                                          .validate() &&
                                                      startTime != null &&
                                                      endTime != null) {
                                                    if (endTime!
                                                            .toDate()
                                                            .difference(
                                                              startTime!
                                                                  .toDate(),
                                                            )
                                                            .inDays <=
                                                        0) {
                                                      final snackBar = SnackBar(
                                                        elevation: 0,
                                                        backgroundColor:
                                                            Colors.transparent,
                                                        behavior:
                                                            SnackBarBehavior
                                                                .floating,
                                                        content:
                                                            AwesomeSnackbarContent(
                                                              contentType:
                                                                  ContentType
                                                                      .warning,
                                                              title:
                                                                  'Thông báo',
                                                              message:
                                                                  'Ngày kết thúc phải sau ngày bắt đầu',
                                                            ),
                                                      );
                                                      ScaffoldMessenger.of(
                                                          context,
                                                        )
                                                        ..hideCurrentSnackBar()
                                                        ..showSnackBar(
                                                          snackBar,
                                                        );
                                                      return;
                                                    }

                                                    setState(() {
                                                      _isSubmitting = true;
                                                    });

                                                    try {
                                                      final rental = Rental(
                                                        id:
                                                            UniqueKey()
                                                                .toString(),
                                                        bikeId: widget.bikeId,
                                                        userId: widget.userId,
                                                        quantity: quantity,
                                                        createdAt:
                                                            createdAt!.toDate(),
                                                        totalAmount:
                                                            totalAmount,
                                                        startTime:
                                                            startTime!.toDate(),
                                                        endTime:
                                                            endTime!.toDate(),
                                                        status: 'Ongoing',
                                                        returnedDate: null,
                                                        cancelledAt: null,
                                                      );

                                                      await _rentalService
                                                          .addRental(rental);
                                                      await _bikeService
                                                          .updateBikeQuantity(
                                                            widget.bikeId,
                                                            bike!.quantity -
                                                                quantity,
                                                          );
                                                      final snackBar = SnackBar(
                                                        elevation: 0,
                                                        backgroundColor:
                                                            Colors.transparent,
                                                        behavior:
                                                            SnackBarBehavior
                                                                .floating,
                                                        content:
                                                            AwesomeSnackbarContent(
                                                              contentType:
                                                                  ContentType
                                                                      .success,
                                                              title:
                                                                  'Thành công',
                                                              message:
                                                                  'Đơn thuê đã được tạo thành công',
                                                            ),
                                                      );
                                                      ScaffoldMessenger.of(
                                                          context,
                                                        )
                                                        ..hideCurrentSnackBar()
                                                        ..showSnackBar(
                                                          snackBar,
                                                        );

                                                      Navigator.pushNamedAndRemoveUntil(
                                                        context,
                                                        '/home', // Route to HomeScreen
                                                        (route) => false,
                                                        arguments:
                                                            2, // Index 2 corresponds to the Rentals tab
                                                      );
                                                    } catch (e) {
                                                      final snackBar = SnackBar(
                                                        elevation: 0,
                                                        backgroundColor:
                                                            Colors.transparent,
                                                        behavior:
                                                            SnackBarBehavior
                                                                .floating,
                                                        content:
                                                            AwesomeSnackbarContent(
                                                              contentType:
                                                                  ContentType
                                                                      .failure,
                                                              title: 'Lỗi',
                                                              message:
                                                                  'Đã xảy ra lỗi khi tạo đơn thuê',
                                                            ),
                                                      );
                                                      ScaffoldMessenger.of(
                                                          context,
                                                        )
                                                        ..hideCurrentSnackBar()
                                                        ..showSnackBar(
                                                          snackBar,
                                                        );
                                                    } finally {
                                                      setState(() {
                                                        _isSubmitting = false;
                                                      });
                                                    }
                                                  } else if (startTime ==
                                                          null ||
                                                      endTime == null) {
                                                    final snackBar = SnackBar(
                                                      elevation: 0,
                                                      backgroundColor:
                                                          Colors.transparent,
                                                      behavior:
                                                          SnackBarBehavior
                                                              .floating,
                                                      content:
                                                          AwesomeSnackbarContent(
                                                            title: 'Thông báo',
                                                            message:
                                                                'Vui lòng chọn ngày bắt đầu và ngày kết thúc',
                                                            contentType:
                                                                ContentType
                                                                    .warning,
                                                          ),
                                                    );
                                                    ScaffoldMessenger.of(
                                                        context,
                                                      )
                                                      ..hideCurrentSnackBar()
                                                      ..showSnackBar(snackBar);
                                                  }
                                                },
                                        child:
                                            _isSubmitting
                                                ? Center(
                                                  child:
                                                      LoadingAnimationWidget.fourRotatingDots(
                                                        color:
                                                            Theme.of(context)
                                                                .colorScheme
                                                                .onPrimary,
                                                        size: 30,
                                                      ),
                                                )
                                                : Text(
                                                  'Xác nhận thuê',
                                                  style: theme
                                                      .textTheme
                                                      .titleMedium
                                                      ?.copyWith(
                                                        fontWeight:
                                                            FontWeight.bold,
                                                        color:
                                                            theme
                                                                .colorScheme
                                                                .onPrimary,
                                                      ),
                                                ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
    );
  }

  Widget _infoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: TextStyle(
                fontWeight: FontWeight.w500,
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
          ),
          Expanded(
            child: Text(value, style: TextStyle(fontWeight: FontWeight.bold)),
          ),
        ],
      ),
    );
  }

  Widget _datePickerField({
    required String label,
    required DateTime? value,
    required VoidCallback onTap,
  }) {
    final dateFormat = DateFormat('dd/MM/yyyy');

    return InkWell(
      onTap: onTap,
      child: InputDecorator(
        decoration: InputDecoration(
          labelText: label,
          prefixIconColor: Theme.of(context).colorScheme.primary,
          prefixIcon: Icon(Icons.calendar_today),
          border: OutlineInputBorder(borderRadius: BorderRadius.circular(10)),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(10),
            borderSide: BorderSide(
              color: Theme.of(context).colorScheme.primary,
            ),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(10),
            borderSide: BorderSide(
              color: Theme.of(context).colorScheme.primary,
              width: 2,
            ),
          ),
        ),
        child: Text(
          value != null ? dateFormat.format(value) : 'Chọn ngày',
          style: TextStyle(color: value != null ? Colors.black : Colors.grey),
        ),
      ),
    );
  }
}
