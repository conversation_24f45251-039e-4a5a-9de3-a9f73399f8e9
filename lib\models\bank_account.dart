import 'package:cloud_firestore/cloud_firestore.dart';

class BankAccount {
  final String id;
  final String bankName;
  final String accountNumber;
  final String accountName;
  final String qrImageUrl;
  final bool isActive;
  final DateTime createdAt;
  final DateTime? updatedAt;

  BankAccount({
    required this.id,
    required this.bankName,
    required this.accountNumber,
    required this.accountName,
    required this.qrImageUrl,
    required this.isActive,
    required this.createdAt,
    this.updatedAt,
  });

  factory BankAccount.fromMap(String id, Map<String, dynamic> map) {
    return BankAccount(
      id: id,
      bankName: map['bankName'] ?? '',
      accountNumber: map['accountNumber'] ?? '',
      accountName: map['accountName'] ?? '',
      qrImageUrl: map['qrImageUrl'] ?? '',
      isActive: map['isActive'] ?? true,
      createdAt: (map['createdAt'] as Timestamp).toDate(),
      updatedAt: map['updatedAt'] != null
          ? (map['updatedAt'] as Timestamp).toDate()
          : null,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'bankName': bankName,
      'accountNumber': accountNumber,
      'accountName': accountName,
      'qrImageUrl': qrImageUrl,
      'isActive': isActive,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': updatedAt != null ? Timestamp.fromDate(updatedAt!) : null,
    };
  }

  BankAccount copyWith({
    String? id,
    String? bankName,
    String? accountNumber,
    String? accountName,
    String? qrImageUrl,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return BankAccount(
      id: id ?? this.id,
      bankName: bankName ?? this.bankName,
      accountNumber: accountNumber ?? this.accountNumber,
      accountName: accountName ?? this.accountName,
      qrImageUrl: qrImageUrl ?? this.qrImageUrl,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
