import 'package:awesome_snackbar_content/awesome_snackbar_content.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:bike_rental_app/models/staff.dart';

class AuthService {
  static final AuthService _instance = AuthService._internal();
  factory AuthService() => _instance;
  AuthService._internal();

  final FirebaseAuth _auth = FirebaseAuth.instance;
  final GoogleSignIn _googleSignIn = GoogleSignIn();
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Current logged in staff
  Staff? _currentStaff;
  bool get isAdmin => _currentStaff?.role == 'admin';
  Staff? get currentStaff => _currentStaff;

  // Session keys for SharedPreferences
  static const String _userIdKey = 'user_id';
  static const String _userEmailKey = 'user_email';
  static const String _userRoleKey = 'user_role';
  static const String _userNameKey = 'user_name';

  // Kiểm tra xem người dùng đã đăng nhập chưa
  Future<bool> isLoggedIn() async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    final String? userId = prefs.getString(_userIdKey);

    if (userId != null) {
      try {
        // Get current user data from Firestore
        final userDoc = await _firestore.collection('staff').doc(userId).get();
        if (userDoc.exists) {
          _currentStaff = Staff.fromJson({
            'id': userId,
            ...userDoc.data() as Map<String, dynamic>,
          });
          return true;
        }
      } catch (e) {
        print('Error retrieving user data: $e');
      }
    }

    return false;
  }

  // Save user session
  Future<void> _saveSession(Staff staff) async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setString(_userIdKey, staff.id);
    await prefs.setString(_userEmailKey, staff.email);
    await prefs.setString(_userRoleKey, staff.role);
    await prefs.setString(_userNameKey, staff.name);
    _currentStaff = staff;
  }

  // Clear session on logout
  Future<void> _clearSession() async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.remove(_userIdKey);
    await prefs.remove(_userEmailKey);
    await prefs.remove(_userRoleKey);
    await prefs.remove(_userNameKey);
    _currentStaff = null;
  }

  // Đăng nhập bằng Email và mật khẩu
  Future<Map<String, dynamic>> loginWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    Map<String, dynamic> result = {
      'success': false,
      'emailError': null,
      'passError': null,
      'message': null,
    };

    try {
      // Authenticate with Firebase
      UserCredential userCredential = await _auth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      // Check if user exists in staff collection
      final userDoc =
          await _firestore
              .collection('staff')
              .doc(userCredential.user!.uid)
              .get();

      if (userDoc.exists) {
        // Create staff object
        Staff staff = Staff.fromJson({
          'id': userCredential.user!.uid,
          ...userDoc.data() as Map<String, dynamic>,
        });

        // Check if staff is active
        if (!staff.isActive) {
          result['message'] =
              'Tài khoản của bạn đã bị vô hiệu hóa. Vui lòng liên hệ với quản trị viên.';
          await _auth.signOut();
          return result;
        }

        // Save session and update current staff
        await _saveSession(staff);
        _currentStaff = staff; // Ensure _currentStaff is updated
        result['success'] = true;
      } else {
        // User exists in Auth but not in staff collection
        result['message'] =
            'Tài khoản không tồn tại trong hệ thống nhân viên. Vui lòng liên hệ với quản trị viên.';
        await _auth.signOut();
      }
    } on FirebaseAuthException catch (e) {
      if (e.code == 'invalid-email') {
        result['emailError'] = 'Vui lòng nhập đúng định dạng email';
      } else if (e.code == 'wrong-password') {
        result['passError'] = 'Mật khẩu không đúng';
      } else if (e.code == 'user-not-found') {
        result['message'] = 'Không tìm thấy tài khoản';
      } else if (e.code == 'invalid-credential') {
        result['message'] =
            'Thông tin đăng nhập không hợp lệ. Vui lòng kiểm tra email và mật khẩu.';
      } else {
        result['message'] = 'Lỗi đăng nhập: ${e.message}'; // Xử lý các lỗi khác
      }
    } catch (e) {
      result['message'] = 'Đã xảy ra lỗi không xác định';
    }

    return result;
  }

  // Tạo người dùng mới với email và mật khẩu
  Future<Map<String, dynamic>> createUser({
    required String email,
    required String password,
    required BuildContext context,
    required Function onSuccess,
  }) async {
    Map<String, dynamic> result = {'success': false, 'error': null};

    try {
      await _auth
          .createUserWithEmailAndPassword(
            email: email.trim(),
            password: password.trim(),
          )
          .whenComplete(() {
            if (_auth.currentUser?.uid != null) {
              sendVerificationEmail();
              final snackBar = SnackBar(
                elevation: 0,
                backgroundColor: Colors.transparent,
                behavior: SnackBarBehavior.floating,
                content: AwesomeSnackbarContent(
                  title: 'Thành công',
                  message:
                      'Email xác thực đã được gửi đến địa chỉ email của bạn. Vui lòng kiểm tra và xác thực tài khoản.',
                  contentType: ContentType.success,
                ),
              );
              ScaffoldMessenger.of(context)
                ..hideCurrentSnackBar()
                ..showSnackBar(snackBar);

              result['success'] = true;
              onSuccess();
            }
          });
    } on FirebaseAuthException catch (e) {
      if (e.code == 'weak-password') {
        result['error'] = 'Mật khẩu quá yếu';
      } else if (e.code == 'email-already-in-use') {
        result['error'] = 'Email đã được sử dụng';
      } else if (e.code == 'invalid-email') {
        result['error'] = 'Email không hợp lệ';
      } else {
        result['error'] = e.message;
      }
    } catch (e) {
      result['error'] = e.toString();
    }

    return result;
  }

  // Gửi email xác thực
  void sendVerificationEmail() {
    User? user = _auth.currentUser;
    if (user != null && !user.emailVerified) {
      user.sendEmailVerification();
    }
  }

  // Kiểm tra email đã xác thực chưa
  bool isEmailVerified() {
    User? user = _auth.currentUser;
    return user != null && user.emailVerified;
  }

  // Đăng nhập bằng số điện thoại
  Future<Map<String, dynamic>> signInWithPhoneNumber({
    required String phoneNumber,
    required Function(String verificationId, int? resendToken) codeSent,
    required Function(PhoneAuthCredential credential) verificationCompleted,
    required Function(FirebaseAuthException e) verificationFailed,
    required Function(String verificationId) codeAutoRetrievalTimeout,
  }) async {
    await _auth.verifyPhoneNumber(
      phoneNumber: phoneNumber,
      verificationCompleted: verificationCompleted,
      verificationFailed: verificationFailed,
      codeSent: codeSent,
      codeAutoRetrievalTimeout: codeAutoRetrievalTimeout,
    );

    return {'success': true};
  }

  // Đăng nhập bằng Google
  Future<UserCredential?> signInWithGoogle() async {
    try {
      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();

      if (googleUser == null) return null;

      final GoogleSignInAuthentication googleAuth =
          await googleUser.authentication;

      final credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );

      return await _auth.signInWithCredential(credential);
    } catch (e) {
      print('Lỗi đăng nhập Google: $e');
      return null;
    }
  }

  // Xác thực OTP
  Future<UserCredential?> verifyOTP({
    required String verificationId,
    required String smsCode,
  }) async {
    try {
      PhoneAuthCredential credential = PhoneAuthProvider.credential(
        verificationId: verificationId,
        smsCode: smsCode,
      );
      return await _auth.signInWithCredential(credential);
    } catch (e) {
      print('Lỗi xác thực OTP: $e');
      return null;
    }
  }

  // Đặt lại mật khẩu
  Future<bool> resetPassword(String email) async {
    try {
      await _auth.sendPasswordResetEmail(email: email);
      return true;
    } catch (e) {
      print('Error resetting password: $e');
      return false;
    }
  }

  // Đăng xuất
  Future<void> signOut() async {
    await _clearSession();
    await _auth.signOut();
  }

  // Lấy người dùng hiện tại
  User? getCurrentUser() {
    return _auth.currentUser;
  }

  // Create new staff (admin only function)
  Future<Map<String, dynamic>> createStaff({
    required String email,
    required String password,
    required String name,
    required String role,
    String? phoneNumber,
    String? avatar,
  }) async {
    Map<String, dynamic> result = {'success': false, 'error': null};

    // Check if current user is admin
    if (_currentStaff?.role != 'admin') {
      result['error'] = 'Chỉ quản trị viên có thể tạo tài khoản nhân viên';
      return result;
    }

    try {
      // Create user in Firebase Auth
      UserCredential userCredential = await _auth
          .createUserWithEmailAndPassword(
            email: email.trim(),
            password: password.trim(),
          );

      // Create staff document in Firestore
      final staff = Staff(
        id: userCredential.user!.uid,
        email: email,
        name: name,
        role: role,
        phoneNumber: phoneNumber,
        avatar: avatar,
        createdAt: DateTime.now(),
        isActive: true,
      );

      await _firestore.collection('staff').doc(staff.id).set(staff.toJson());

      result['success'] = true;
    } on FirebaseAuthException catch (e) {
      if (e.code == 'weak-password') {
        result['error'] = 'The password provided is too weak';
      } else if (e.code == 'email-already-in-use') {
        result['error'] = 'The account already exists for that email';
      } else if (e.code == 'invalid-email') {
        result['error'] = 'The email address is not valid';
      } else {
        result['error'] = e.message;
      }
    } catch (e) {
      result['error'] = e.toString();
    }

    return result;
  }

  // Get staff list (admin function)
  Future<List<Staff>> getStaffList() async {
    if (_currentStaff?.role != 'admin') {
      return [];
    }

    try {
      final querySnapshot = await _firestore.collection('staff').get();
      return querySnapshot.docs
          .map((doc) => Staff.fromJson({'id': doc.id, ...doc.data()}))
          .toList();
    } catch (e) {
      print('Error fetching staff list: $e');
      return [];
    }
  }

  // Update staff data (admin function)
  Future<bool> updateStaff(Staff staff) async {
    if (_currentStaff?.role != 'admin') {
      return false;
    }

    try {
      await _firestore.collection('staff').doc(staff.id).update(staff.toJson());
      return true;
    } catch (e) {
      print('Error updating staff: $e');
      return false;
    }
  }
}
