import 'package:awesome_snackbar_content/awesome_snackbar_content.dart';
import 'package:bike_rental_app/models/staff.dart';
import 'package:bike_rental_app/services/auth_service.dart';
import 'package:bike_rental_app/services/staff_service.dart';
import 'package:bike_rental_app/services/storage_service.dart';
import 'package:bike_rental_app/utils/animation_helper.dart';
import 'package:bike_rental_app/widgets/custom_text_form_field.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:bike_rental_app/widgets/common_widgets.dart';
import 'package:panara_dialogs/panara_dialogs.dart';
import 'dart:io';

class StaffManagementScreen extends StatefulWidget {
  final bool showBackButton;

  const StaffManagementScreen({Key? key, this.showBackButton = true})
    : super(key: key);

  @override
  _StaffManagementScreenState createState() => _StaffManagementScreenState();
}

class _StaffManagementScreenState extends State<StaffManagementScreen> {
  final AuthService _authService = AuthService();
  late final StaffService _staffService;
  final StorageService _storageService = StorageService();
  final ImagePicker _imagePicker = ImagePicker();
  bool _isLoading = true;
  List<Staff> _staffList = [];
  String _searchQuery = '';
  bool _isSearching = false;
  String _roleFilter = 'all';
  String _statusFilter = 'all';

  @override
  void initState() {
    super.initState();
    _staffService = StaffService(authService: _authService);
    _initializeAuth();
  }

  Future<void> _initializeAuth() async {
    await _authService.isLoggedIn();

    if (mounted) {
      _loadStaffList();
    }
  }

  Future<void> _loadStaffList() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
    });

    if (!_authService.isAdmin) {
      if (mounted) {
        final snackBar = SnackBar(
          elevation: 0,
          behavior: SnackBarBehavior.floating,
          backgroundColor: Colors.transparent,
          content: AwesomeSnackbarContent(
            title: 'Từ chối truy cập',
            message: 'Bạn không có quyền truy cập trang này',
            contentType: ContentType.failure,
          ),
        );
        ScaffoldMessenger.of(context)
          ..hideCurrentSnackBar()
          ..showSnackBar(snackBar);
        Navigator.pop(context);
      }
      return;
    }

    try {
      final staffList = await _staffService.getStaffList();
      // Sắp xếp theo thứ tự tạo mới nhất lên đầu
      staffList.sort((a, b) => b.createdAt.compareTo(a.createdAt));

      if (mounted) {
        setState(() {
          _staffList = staffList;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        final snackBar = SnackBar(
          elevation: 0,
          behavior: SnackBarBehavior.floating,
          backgroundColor: Colors.transparent,
          content: AwesomeSnackbarContent(
            title: 'Error',
            message: 'Lỗi khi tải danh sách nhân viên: $e',
            contentType: ContentType.failure,
          ),
        );
        ScaffoldMessenger.of(context)
          ..hideCurrentSnackBar()
          ..showSnackBar(snackBar);
      }
    }
  }

  void _toggleSearch() {
    setState(() {
      _isSearching = !_isSearching;
      if (!_isSearching) {
        _searchQuery = '';
      }
    });
  }

  List<Staff> _getFilteredStaffList() {
    List<Staff> filteredList = List.from(_staffList);

    // Áp dụng tìm kiếm
    if (_searchQuery.isNotEmpty) {
      filteredList =
          filteredList.where((staff) {
            return staff.name.toLowerCase().contains(
                  _searchQuery.toLowerCase(),
                ) ||
                staff.email.toLowerCase().contains(
                  _searchQuery.toLowerCase(),
                ) ||
                (staff.phoneNumber != null &&
                    staff.phoneNumber!.toLowerCase().contains(
                      _searchQuery.toLowerCase(),
                    ));
          }).toList();
    }

    // Áp dụng lọc theo vai trò
    if (_roleFilter != 'all') {
      filteredList =
          filteredList.where((staff) => staff.role == _roleFilter).toList();
    }

    // Áp dụng lọc theo trạng thái
    if (_statusFilter != 'all') {
      filteredList =
          filteredList
              .where(
                (staff) =>
                    (_statusFilter == 'active' && staff.isActive) ||
                    (_statusFilter == 'inactive' && !staff.isActive),
              )
              .toList();
    }

    return filteredList;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        elevation: 0,
        backgroundColor: theme.appBarTheme.backgroundColor,
        title:
            _isSearching
                ? TextField(
                  autofocus: true,
                  style: TextStyle(color: theme.colorScheme.primary),
                  decoration: InputDecoration(
                    hintText: 'Tìm kiếm nhân viên...',
                    hintStyle: TextStyle(
                      color: theme.colorScheme.primary.withAlpha(200),
                    ),
                    border: InputBorder.none,
                  ),
                  onChanged: (value) {
                    setState(() {
                      _searchQuery = value;
                    });
                  },
                )
                : const Text('Quản lý nhân viên'),
        leading:
            widget.showBackButton
                ? IconButton(
                  icon: const Icon(Icons.arrow_back),
                  onPressed: () => Navigator.pop(context),
                )
                : null,
        actions: [
          IconButton(
            icon: Icon(
              _isSearching ? Icons.close : Icons.search,
              color: theme.colorScheme.primary,
            ),
            onPressed: _toggleSearch,
          ),
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: () {
              _showFilterDialog(context);
            },
          ),
        ],
      ),
      body:
          _isLoading
              ? Center(
                child: AppLoadingIndicator(
                  color: theme.colorScheme.primary,
                  size: 50,
                  message: 'Đang tải dữ liệu...',
                ),
              )
              : Column(
                children: [
                  if (_roleFilter != 'all' || _statusFilter != 'all')
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 8,
                      ),
                      child: Row(
                        children: [
                          Text(
                            'Bộ lọc: ' +
                                (_roleFilter != 'all'
                                    ? (_roleFilter == 'staff'
                                        ? 'Nhân viên'
                                        : 'Quản trị viên')
                                    : '') +
                                (_roleFilter != 'all' && _statusFilter != 'all'
                                    ? ' - '
                                    : '') +
                                (_statusFilter != 'all'
                                    ? (_statusFilter == 'active'
                                        ? 'Đang hoạt động'
                                        : 'Không hoạt động')
                                    : ''),
                            style: theme.textTheme.bodyMedium,
                          ),
                          const Spacer(),
                          TextButton(
                            onPressed: () {
                              setState(() {
                                _roleFilter = 'all';
                                _statusFilter = 'all';
                              });
                            },
                            child: const Text('Xóa bộ lọc'),
                          ),
                        ],
                      ),
                    ),
                  Expanded(
                    child: RefreshIndicator(
                      onRefresh: _loadStaffList,
                      child:
                          _getFilteredStaffList().isEmpty
                              ? AnimationHelper.fadeIn(
                                child: Center(
                                  child: Text(
                                    'Không tìm thấy nhân viên',
                                    style: theme.textTheme.bodyLarge,
                                  ),
                                ),
                              )
                              : ListView.builder(
                                itemCount: _getFilteredStaffList().length,
                                itemBuilder: (context, index) {
                                  final staff = _getFilteredStaffList()[index];
                                  return AnimationHelper.fadeInUp(
                                    delay: Duration(milliseconds: 100 * index),
                                    child: StaffListItem(
                                      staff: staff,
                                      onRefresh: _loadStaffList,
                                      authService: _authService,
                                    ),
                                  );
                                },
                              ),
                    ),
                  ),
                ],
              ),
      floatingActionButton: AnimationHelper.scale(
        child: FloatingActionButton(
          onPressed: () {
            _showAddStaffDialog(context);
          },
          child: const Icon(Icons.add),
        ),
      ),
    );
  }

  void _showFilterDialog(BuildContext context) {
    showDialog(
      context: context,
      builder:
          (context) => AnimationHelper.fadeInUp(
            child: AlertDialog(
              title: const Text('Bộ lọc'),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Vai trò:',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  DropdownButtonFormField<String>(
                    value: _roleFilter,
                    decoration: const InputDecoration(
                      border: OutlineInputBorder(),
                      contentPadding: EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 8,
                      ),
                    ),
                    items: const [
                      DropdownMenuItem(value: 'all', child: Text('Tất cả')),
                      DropdownMenuItem(
                        value: 'admin',
                        child: Text('Quản trị viên'),
                      ),
                      DropdownMenuItem(
                        value: 'staff',
                        child: Text('Nhân viên'),
                      ),
                    ],
                    onChanged: (value) {
                      setState(() {
                        _roleFilter = value!;
                      });
                    },
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    'Trạng thái:',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  DropdownButtonFormField<String>(
                    value: _statusFilter,
                    decoration: const InputDecoration(
                      border: OutlineInputBorder(),
                      contentPadding: EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 8,
                      ),
                    ),
                    items: const [
                      DropdownMenuItem(value: 'all', child: Text('Tất cả')),
                      DropdownMenuItem(
                        value: 'active',
                        child: Text('Đang hoạt động'),
                      ),
                      DropdownMenuItem(
                        value: 'inactive',
                        child: Text('Không hoạt động'),
                      ),
                    ],
                    onChanged: (value) {
                      setState(() {
                        _statusFilter = value!;
                      });
                    },
                  ),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    setState(() {
                      _roleFilter = 'all';
                      _statusFilter = 'all';
                    });
                    Navigator.pop(context);
                  },
                  child: const Text('Đặt lại'),
                ),
                TextButton(
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  child: const Text('Áp dụng'),
                ),
              ],
            ),
          ),
    );
  }

  void _showAddStaffDialog(BuildContext context) {
    final formKey = GlobalKey<FormState>();
    final nameController = TextEditingController();
    final emailController = TextEditingController();
    final passwordController = TextEditingController();
    final phoneController = TextEditingController();
    String selectedRole = 'staff'; // Vai trò mặc định
    File? _selectedImage;

    showDialog(
      context: context,
      builder:
          (dialogContext) => AnimationHelper.fadeInUp(
            child: AlertDialog(
              title: const Text('Thêm nhân viên mới'),
              content: Form(
                key: formKey,
                child: SingleChildScrollView(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      CustomTextFormField(
                        controller: nameController,
                        label: 'Họ và tên',
                        prefixIcon: Icons.person,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Vui lòng nhập tên';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),
                      CustomTextFormField(
                        controller: emailController,
                        label: 'Email',
                        prefixIcon: Icons.email,
                        keyboardType: TextInputType.emailAddress,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Vui lòng nhập email';
                          }
                          if (!value.contains('@') || !value.contains('.')) {
                            return 'Vui lòng nhập email hợp lệ';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),
                      CustomTextFormField(
                        controller: passwordController,
                        label: 'Mật khẩu',
                        prefixIcon: Icons.lock,
                        obscureText: true,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Vui lòng nhập mật khẩu';
                          }
                          if (value.length < 6) {
                            return 'Mật khẩu phải có ít nhất 6 ký tự';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),
                      CustomTextFormField(
                        controller: phoneController,
                        label: 'Số điện thoại (không bắt buộc)',
                        prefixIcon: Icons.phone,
                        keyboardType: TextInputType.phone,
                      ),
                      const SizedBox(height: 16),
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.symmetric(vertical: 8),
                        decoration: BoxDecoration(
                          border: Border.all(
                            color: Colors.grey.withOpacity(0.5),
                          ),
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: Column(
                          children: [
                            Text(
                              'Ảnh đại diện',
                              style: TextStyle(
                                color: Theme.of(context).colorScheme.primary,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 8),
                            StatefulBuilder(
                              builder: (context, setInnerState) {
                                return Column(
                                  children: [
                                    if (_selectedImage != null)
                                      Container(
                                        width: 100,
                                        height: 100,
                                        decoration: BoxDecoration(
                                          shape: BoxShape.circle,
                                          image: DecorationImage(
                                            image: FileImage(_selectedImage!),
                                            fit: BoxFit.cover,
                                          ),
                                        ),
                                      )
                                    else
                                      Container(
                                        width: 100,
                                        height: 100,
                                        decoration: BoxDecoration(
                                          shape: BoxShape.circle,
                                          color: Colors.grey.withOpacity(0.2),
                                        ),
                                        child: Icon(
                                          Icons.person,
                                          size: 50,
                                          color: Colors.grey.withOpacity(0.7),
                                        ),
                                      ),
                                    const SizedBox(height: 8),
                                    ElevatedButton.icon(
                                      onPressed: () async {
                                        final XFile? pickedImage =
                                            await _imagePicker.pickImage(
                                              source: ImageSource.gallery,
                                              imageQuality: 80,
                                            );
                                        if (pickedImage != null) {
                                          setInnerState(() {
                                            _selectedImage = File(
                                              pickedImage.path,
                                            );
                                          });
                                        }
                                      },
                                      icon: const Icon(Icons.photo_library),
                                      label: const Text('Chọn ảnh'),
                                    ),
                                  ],
                                );
                              },
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 16),
                      DropdownButtonFormField<String>(
                        value: selectedRole,
                        decoration: const InputDecoration(labelText: 'Vai trò'),
                        items: const [
                          DropdownMenuItem(
                            value: 'admin',
                            child: Text('Quản trị viên'),
                          ),
                          DropdownMenuItem(
                            value: 'staff',
                            child: Text('Nhân viên'),
                          ),
                        ],
                        onChanged: (value) {
                          selectedRole = value!;
                        },
                      ),
                    ],
                  ),
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(dialogContext),
                  child: const Text('Hủy'),
                ),
                TextButton(
                  onPressed: () async {
                    if (formKey.currentState!.validate()) {
                      Navigator.pop(
                        dialogContext,
                      ); // Đóng dialog nhập thông tin

                      // Hiển thị dialog tải
                      showDialog(
                        context: context,
                        barrierDismissible: false,
                        builder:
                            (loadingContext) => const Center(
                              child: CircularProgressIndicator(),
                            ),
                      );

                      // Tải ảnh lên nếu có
                      String? avatarUrl;
                      if (_selectedImage != null) {
                        try {
                          final String fileName =
                              'staff_${DateTime.now().millisecondsSinceEpoch}.jpg';
                          avatarUrl = await _storageService.uploadFile(
                            _selectedImage!,
                            fileName,
                          );
                        } catch (e) {
                          final snackBar = SnackBar(
                            elevation: 0,
                            behavior: SnackBarBehavior.floating,
                            backgroundColor: Colors.transparent,
                            content: AwesomeSnackbarContent(
                              title: 'Lỗi',
                              message: 'Lỗi khi tải ảnh nhân viên: $e',
                              contentType: ContentType.failure,
                            ),
                          );
                          ScaffoldMessenger.of(context)
                            ..hideCurrentSnackBar()
                            ..showSnackBar(snackBar);
                        }
                      }

                      // Thực hiện tạo nhân viên
                      final result = await _staffService.createStaff(
                        email: emailController.text,
                        password: passwordController.text,
                        name: nameController.text,
                        role: selectedRole,
                        phoneNumber:
                            phoneController.text.isNotEmpty
                                ? phoneController.text
                                : null,
                        avatar: avatarUrl,
                      );

                      // Kiểm tra context còn hợp lệ trước khi sử dụng
                      if (!context.mounted) return;

                      // Đóng dialog tải
                      Navigator.pop(context); // Đóng dialog tải

                      if (result['success']) {
                        final snackBar = SnackBar(
                          elevation: 0,
                          behavior: SnackBarBehavior.floating,
                          backgroundColor: Colors.transparent,
                          content: AwesomeSnackbarContent(
                            title: 'Thành công',
                            message: 'Thêm nhân viên thành công',
                            contentType: ContentType.success,
                          ),
                        );
                        ScaffoldMessenger.of(context)
                          ..hideCurrentSnackBar()
                          ..showSnackBar(snackBar);
                        _loadStaffList();
                      } else {
                        final snackBar = SnackBar(
                          elevation: 0,
                          behavior: SnackBarBehavior.floating,
                          backgroundColor: Colors.transparent,
                          content: AwesomeSnackbarContent(
                            title: 'Lỗi',
                            message:
                                'Lỗi khi thêm nhân viên: ${result['error']}',
                            contentType: ContentType.failure,
                          ),
                        );
                        ScaffoldMessenger.of(context)
                          ..hideCurrentSnackBar()
                          ..showSnackBar(snackBar);
                      }
                    }
                  },
                  child: const Text('Thêm'),
                ),
              ],
            ),
          ),
    );
  }
}

class StaffListItem extends StatelessWidget {
  final Staff staff;
  final Function onRefresh;
  final AuthService authService;
  late final StaffService staffService;

  StaffListItem({
    super.key,
    required this.staff,
    required this.onRefresh,
    required this.authService,
  }) {
    staffService = StaffService(authService: authService);
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                // Avatar
                staff.avatar != null && staff.avatar!.isNotEmpty
                    ? CircleAvatar(
                      radius: 25,
                      backgroundImage: NetworkImage(staff.avatar!),
                      backgroundColor: Theme.of(context).primaryColor,
                    )
                    : CircleAvatar(
                      radius: 25,
                      backgroundColor: Theme.of(context).primaryColor,
                      child: Text(
                        staff.name.isNotEmpty
                            ? staff.name[0].toUpperCase()
                            : '?',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 18,
                        ),
                      ),
                    ),
                const SizedBox(width: 16),
                // Staff information
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        staff.name,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        staff.email,
                        style: TextStyle(color: Colors.grey[700], fontSize: 14),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            // Status badges
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 2,
                  ),
                  decoration: BoxDecoration(
                    color:
                        staff.role == 'admin'
                            ? Colors.red.withOpacity(0.2)
                            : Colors.blue.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Text(
                    staff.role.toUpperCase(),
                    style: TextStyle(
                      fontSize: 12,
                      color: staff.role == 'admin' ? Colors.red : Colors.blue,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 2,
                  ),
                  decoration: BoxDecoration(
                    color:
                        staff.isActive
                            ? Colors.green.withOpacity(0.2)
                            : Colors.grey.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Text(
                    staff.isActive ? 'ACTIVE' : 'INACTIVE',
                    style: TextStyle(
                      fontSize: 12,
                      color: staff.isActive ? Colors.green : Colors.grey,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const Spacer(),
                // Action buttons
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    IconButton(
                      icon: const Icon(
                        Icons.edit,
                        color: Colors.blue,
                        size: 22,
                      ),
                      constraints: const BoxConstraints(),
                      padding: const EdgeInsets.all(8),
                      onPressed: () {
                        _showEditStaffDialog(context, staff);
                      },
                    ),
                    IconButton(
                      icon: const Icon(
                        Icons.delete,
                        color: Colors.red,
                        size: 22,
                      ),
                      constraints: const BoxConstraints(),
                      padding: const EdgeInsets.all(8),
                      onPressed: () {
                        PanaraConfirmDialog.show(
                          context,
                          title: 'Xóa nhân viên',
                          message: 'Bạn có chắc chắn muốn xóa nhân viên này?',
                          confirmButtonText: 'Xóa',
                          cancelButtonText: 'Hủy',
                          onTapCancel: () {
                            Navigator.pop(context);
                          },
                          onTapConfirm: () async {
                            Navigator.pop(context);
                            final result = await staffService.deleteStaff(
                              staff.id,
                            );

                            if (result['success']) {
                              final snackBar = SnackBar(
                                elevation: 0,
                                behavior: SnackBarBehavior.floating,
                                backgroundColor: Colors.transparent,
                                content: AwesomeSnackbarContent(
                                  title: 'Thành công',
                                  message: 'Xóa nhân viên thành công',
                                  contentType: ContentType.success,
                                ),
                              );
                              ScaffoldMessenger.of(context)
                                ..hideCurrentSnackBar()
                                ..showSnackBar(snackBar);
                              onRefresh();
                            } else {
                              final snackBar = SnackBar(
                                elevation: 0,
                                behavior: SnackBarBehavior.floating,
                                backgroundColor: Colors.transparent,
                                content: AwesomeSnackbarContent(
                                  title: 'Lỗi',
                                  message:
                                      'Lỗi khi xóa nhân viên: ${result['error']}',
                                  contentType: ContentType.failure,
                                ),
                              );
                              ScaffoldMessenger.of(context)
                                ..hideCurrentSnackBar()
                                ..showSnackBar(snackBar);
                            }
                          },
                          panaraDialogType: PanaraDialogType.normal,
                        );
                      },
                    ),
                    IconButton(
                      icon: Icon(
                        staff.isActive ? Icons.block : Icons.check_circle,
                        color: staff.isActive ? Colors.red : Colors.green,
                        size: 22,
                      ),
                      constraints: const BoxConstraints(),
                      padding: const EdgeInsets.all(8),
                      onPressed: () {
                        PanaraConfirmDialog.show(
                          context,
                          title:
                              staff.isActive
                                  ? 'Vô hiệu hóa nhân viên'
                                  : 'Kích hoạt nhân viên',
                          message:
                              staff.isActive
                                  ? 'Bạn có chắc chắn muốn vô hiệu hóa nhân viên này?'
                                  : 'Bạn có chắc chắn muốn kích hoạt nhân viên này?',
                          confirmButtonText: 'Đồng ý',
                          cancelButtonText: 'Hủy',
                          onTapCancel: () {
                            Navigator.pop(context);
                          },
                          onTapConfirm: () async {
                            Navigator.pop(context);
                            final updatedStaff = staff.copyWith(
                              isActive: !staff.isActive,
                            );
                            final success = await authService.updateStaff(
                              updatedStaff,
                            );

                            if (success) {
                              final snackBar = SnackBar(
                                elevation: 0,
                                behavior: SnackBarBehavior.floating,
                                backgroundColor: Colors.transparent,
                                content: AwesomeSnackbarContent(
                                  title: 'Thành công',
                                  message:
                                      staff.isActive
                                          ? 'Vô hiệu hóa nhân viên thành công'
                                          : 'Kích hoạt nhân viên thành công',
                                  contentType: ContentType.success,
                                ),
                              );
                              ScaffoldMessenger.of(context)
                                ..hideCurrentSnackBar()
                                ..showSnackBar(snackBar);
                              onRefresh();
                            } else {
                              final snackBar = SnackBar(
                                elevation: 0,
                                behavior: SnackBarBehavior.floating,
                                backgroundColor: Colors.transparent,
                                content: AwesomeSnackbarContent(
                                  title: 'Lỗi',
                                  message:
                                      'Lỗi khi ${staff.isActive ? 'vô hiệu hóa' : 'kích hoạt'} nhân viên',
                                  contentType: ContentType.failure,
                                ),
                              );
                              ScaffoldMessenger.of(context)
                                ..hideCurrentSnackBar()
                                ..showSnackBar(snackBar);
                            }
                          },
                          panaraDialogType: PanaraDialogType.normal,
                        );
                      },
                    ),
                    if (staff.role != 'admin' ||
                        authService.currentStaff!.id != staff.id)
                      IconButton(
                        icon: Icon(
                          staff.role == 'admin'
                              ? Icons.person
                              : Icons.admin_panel_settings,
                          color: Theme.of(context).primaryColor,
                          size: 22,
                        ),
                        constraints: const BoxConstraints(),
                        padding: const EdgeInsets.all(8),
                        onPressed: () {
                          PanaraConfirmDialog.show(
                            context,
                            title: 'Thay đổi vai trò',
                            message:
                                staff.role == 'admin'
                                    ? 'Thay đổi quản trị viên này thành nhân viên?'
                                    : 'Thăng cấp nhân viên này thành quản trị viên?',
                            confirmButtonText: 'Đồng ý',
                            cancelButtonText: 'Hủy',
                            onTapCancel: () {
                              Navigator.pop(context);
                            },
                            onTapConfirm: () async {
                              Navigator.pop(context);
                              final newRole =
                                  staff.role == 'admin' ? 'staff' : 'admin';
                              final updatedStaff = staff.copyWith(
                                role: newRole,
                              );
                              final success = await authService.updateStaff(
                                updatedStaff,
                              );

                              if (success) {
                                final snackBar = SnackBar(
                                  elevation: 0,
                                  behavior: SnackBarBehavior.floating,
                                  backgroundColor: Colors.transparent,
                                  content: AwesomeSnackbarContent(
                                    title: 'Thành công',
                                    message:
                                        'Thay đổi vai trò thành ${newRole == 'admin' ? 'QUẢN TRỊ VIÊN' : 'NHÂN VIÊN'} thành công',
                                    contentType: ContentType.success,
                                  ),
                                );
                                ScaffoldMessenger.of(context)
                                  ..hideCurrentSnackBar()
                                  ..showSnackBar(snackBar);
                                onRefresh();
                              } else {
                                final snackBar = SnackBar(
                                  elevation: 0,
                                  behavior: SnackBarBehavior.floating,
                                  backgroundColor: Colors.transparent,
                                  content: AwesomeSnackbarContent(
                                    title: 'Lỗi',
                                    message:
                                        'Lỗi khi thay đổi vai trò nhân viên',
                                    contentType: ContentType.failure,
                                  ),
                                );
                                ScaffoldMessenger.of(context)
                                  ..hideCurrentSnackBar()
                                  ..showSnackBar(snackBar);
                              }
                            },
                            panaraDialogType: PanaraDialogType.normal,
                          );
                        },
                      ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _showEditStaffDialog(BuildContext context, Staff staff) {
    final formKey = GlobalKey<FormState>();
    final nameController = TextEditingController(text: staff.name);
    final phoneController = TextEditingController(
      text: staff.phoneNumber ?? '',
    );
    String selectedRole = staff.role;
    File? _selectedImage;
    String? currentAvatarUrl = staff.avatar;
    final StorageService _storageService = StorageService();
    final ImagePicker _imagePicker = ImagePicker();

    showDialog(
      context: context,
      builder:
          (dialogContext) => AnimationHelper.fadeInUp(
            child: AlertDialog(
              title: const Text('Chỉnh sửa thông tin nhân viên'),
              content: Form(
                key: formKey,
                child: SingleChildScrollView(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      CustomTextFormField(
                        controller: nameController,
                        label: 'Họ và tên',
                        prefixIcon: Icons.person,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Vui lòng nhập tên';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),
                      CustomTextFormField(
                        controller: phoneController,
                        label: 'Số điện thoại (không bắt buộc)',
                        prefixIcon: Icons.phone,
                        keyboardType: TextInputType.phone,
                      ),
                      const SizedBox(height: 16),
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.symmetric(vertical: 8),
                        decoration: BoxDecoration(
                          border: Border.all(
                            color: Colors.grey.withOpacity(0.5),
                          ),
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: Column(
                          children: [
                            Text(
                              'Ảnh đại diện',
                              style: TextStyle(
                                color: Theme.of(context).colorScheme.primary,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 8),
                            StatefulBuilder(
                              builder: (context, setInnerState) {
                                return Column(
                                  children: [
                                    if (_selectedImage != null)
                                      Container(
                                        width: 100,
                                        height: 100,
                                        decoration: BoxDecoration(
                                          shape: BoxShape.circle,
                                          image: DecorationImage(
                                            image: FileImage(_selectedImage!),
                                            fit: BoxFit.cover,
                                          ),
                                        ),
                                      )
                                    else if (currentAvatarUrl != null &&
                                        currentAvatarUrl.isNotEmpty)
                                      Container(
                                        width: 100,
                                        height: 100,
                                        decoration: BoxDecoration(
                                          shape: BoxShape.circle,
                                          image: DecorationImage(
                                            image: NetworkImage(
                                              currentAvatarUrl,
                                            ),
                                            fit: BoxFit.cover,
                                          ),
                                        ),
                                      )
                                    else
                                      Container(
                                        width: 100,
                                        height: 100,
                                        decoration: BoxDecoration(
                                          shape: BoxShape.circle,
                                          color: Colors.grey.withOpacity(0.2),
                                        ),
                                        child: Icon(
                                          Icons.person,
                                          size: 50,
                                          color: Colors.grey.withOpacity(0.7),
                                        ),
                                      ),
                                    const SizedBox(height: 8),
                                    ElevatedButton.icon(
                                      onPressed: () async {
                                        final XFile? pickedImage =
                                            await _imagePicker.pickImage(
                                              source: ImageSource.gallery,
                                              imageQuality: 80,
                                            );
                                        if (pickedImage != null) {
                                          setInnerState(() {
                                            _selectedImage = File(
                                              pickedImage.path,
                                            );
                                          });
                                        }
                                      },
                                      icon: const Icon(Icons.photo_library),
                                      label: const Text('Chọn ảnh'),
                                    ),
                                  ],
                                );
                              },
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 16),
                      DropdownButtonFormField<String>(
                        value: selectedRole,
                        decoration: const InputDecoration(labelText: 'Vai trò'),
                        items: const [
                          DropdownMenuItem(
                            value: 'admin',
                            child: Text('Quản trị viên'),
                          ),
                          DropdownMenuItem(
                            value: 'staff',
                            child: Text('Nhân viên'),
                          ),
                        ],
                        onChanged: (value) {
                          selectedRole = value!;
                        },
                      ),
                    ],
                  ),
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(dialogContext),
                  child: const Text('Hủy'),
                ),
                TextButton(
                  onPressed: () async {
                    if (formKey.currentState!.validate()) {
                      Navigator.pop(
                        dialogContext,
                      ); // Đóng dialog nhập thông tin

                      // Hiển thị dialog tải
                      showDialog(
                        context: context,
                        barrierDismissible: false,
                        builder:
                            (loadingContext) => const Center(
                              child: CircularProgressIndicator(),
                            ),
                      );

                      // Tải ảnh lên nếu có
                      String? avatarUrl = currentAvatarUrl;
                      if (_selectedImage != null) {
                        try {
                          final String fileName =
                              'staff_${DateTime.now().millisecondsSinceEpoch}.jpg';
                          avatarUrl = await _storageService.uploadFile(
                            _selectedImage!,
                            fileName,
                          );
                        } catch (e) {
                          final snackBar = SnackBar(
                            elevation: 0,
                            behavior: SnackBarBehavior.floating,
                            backgroundColor: Colors.transparent,
                            content: AwesomeSnackbarContent(
                              title: 'Lỗi',
                              message: 'Lỗi khi tải ảnh lên: ${e.toString()}',
                              contentType: ContentType.failure,
                            ),
                          );
                          ScaffoldMessenger.of(context)
                            ..hideCurrentSnackBar()
                            ..showSnackBar(snackBar);
                        }
                      }

                      // Cập nhật thông tin nhân viên
                      final updatedStaff = staff.copyWith(
                        name: nameController.text,
                        phoneNumber:
                            phoneController.text.isNotEmpty
                                ? phoneController.text
                                : null,
                        avatar: avatarUrl,
                        role: selectedRole,
                      );

                      final success = await authService.updateStaff(
                        updatedStaff,
                      );

                      // Kiểm tra context còn hợp lệ trước khi sử dụng
                      if (!context.mounted) return;

                      // Đóng dialog tải
                      Navigator.pop(context); // Đóng dialog tải

                      if (success) {
                        final snackBar = SnackBar(
                          elevation: 0,
                          behavior: SnackBarBehavior.floating,
                          backgroundColor: Colors.transparent,
                          content: AwesomeSnackbarContent(
                            title: 'Thành công',
                            message: 'Cập nhật thông tin nhân viên thành công',
                            contentType: ContentType.success,
                          ),
                        );
                        ScaffoldMessenger.of(context)
                          ..hideCurrentSnackBar()
                          ..showSnackBar(snackBar);
                        onRefresh();
                      } else {
                        final snackBar = SnackBar(
                          elevation: 0,
                          behavior: SnackBarBehavior.floating,
                          backgroundColor: Colors.transparent,
                          content: AwesomeSnackbarContent(
                            title: 'Lỗi',
                            message: 'Lỗi khi cập nhật thông tin nhân viên',
                            contentType: ContentType.failure,
                          ),
                        );
                        ScaffoldMessenger.of(context)
                          ..hideCurrentSnackBar()
                          ..showSnackBar(snackBar);
                      }
                    }
                  },
                  child: const Text('Cập nhật'),
                ),
              ],
            ),
          ),
    );
  }
}
