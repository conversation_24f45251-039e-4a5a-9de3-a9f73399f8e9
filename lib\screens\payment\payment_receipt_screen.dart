import 'package:bike_rental_app/models/payment.dart';
import 'package:bike_rental_app/models/rental.dart';
import 'package:bike_rental_app/models/user.dart';
import 'package:bike_rental_app/services/rental_service.dart';
import 'package:bike_rental_app/services/user_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:intl/intl.dart';
import 'package:loading_animation_widget/loading_animation_widget.dart';
import 'package:share_plus/share_plus.dart';
import 'package:path_provider/path_provider.dart';
import 'dart:io';
import 'dart:typed_data';
import 'dart:ui' as ui;

class PaymentReceiptScreen extends StatefulWidget {
  final Payment payment;

  const PaymentReceiptScreen({Key? key, required this.payment})
    : super(key: key);

  @override
  _PaymentReceiptScreenState createState() => _PaymentReceiptScreenState();
}

class _PaymentReceiptScreenState extends State<PaymentReceiptScreen> {
  final GlobalKey _receiptKey = GlobalKey();
  bool isLoading = true;
  Rental? rental;
  User? user;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    try {
      final rentalService = RentalService();
      final userService = UserService();

      // Fetch rental information
      final fetchedRental = await rentalService.getRentalById(
        widget.payment.rentalId,
      );

      // Fetch user information
      final fetchedUser = await userService.getUserById(fetchedRental.userId);

      setState(() {
        rental = fetchedRental;
        user = fetchedUser;
        isLoading = false;
      });
    } catch (e) {
      print('Error loading data: $e');
      setState(() {
        isLoading = false;
      });
    }
  }

  Future<void> _captureAndShareReceipt() async {
    try {
      // Get the render object from the key
      final RenderRepaintBoundary boundary =
          _receiptKey.currentContext!.findRenderObject()
              as RenderRepaintBoundary;

      // Capture image
      final ui.Image image = await boundary.toImage(pixelRatio: 3.0);
      final ByteData? byteData = await image.toByteData(
        format: ui.ImageByteFormat.png,
      );

      if (byteData != null) {
        final Uint8List pngBytes = byteData.buffer.asUint8List();

        // Create temporary file
        final tempDir = await getTemporaryDirectory();
        final file = await File('${tempDir.path}/receipt.png').create();
        await file.writeAsBytes(pngBytes);

        // Share the receipt
        await Share.shareXFiles([
          XFile(file.path),
        ], text: 'Biên lai thanh toán #${widget.payment.receiptNumber}');
      }
    } catch (e) {
      print('Error capturing receipt: $e');
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('Không thể chia sẻ biên lai: $e')));
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text('Biên lai thanh toán'),
        actions: [
          if (!isLoading)
            IconButton(
              icon: Icon(Icons.share),
              onPressed: _captureAndShareReceipt,
              tooltip: 'Chia sẻ biên lai',
            ),
        ],
      ),
      body:
          isLoading
              ? Center(
                child: LoadingAnimationWidget.fourRotatingDots(
                  color: theme.colorScheme.primary,
                  size: 40,
                ),
              )
              : rental == null || user == null
              ? Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.error_outline, size: 60, color: Colors.red),
                    SizedBox(height: 16),
                    Text('Không thể tải thông tin biên lai'),
                    SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () => Navigator.of(context).pop(),
                      child: Text('Quay lại'),
                    ),
                  ],
                ),
              )
              : SingleChildScrollView(
                padding: EdgeInsets.all(16),
                child: Column(
                  children: [
                    RepaintBoundary(
                      key: _receiptKey,
                      child: Container(
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(8),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.grey.withOpacity(0.3),
                              spreadRadius: 1,
                              blurRadius: 5,
                              offset: Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Receipt header
                            Container(
                              padding: EdgeInsets.all(16),
                              decoration: BoxDecoration(
                                color: theme.colorScheme.primary,
                                borderRadius: BorderRadius.only(
                                  topLeft: Radius.circular(8),
                                  topRight: Radius.circular(8),
                                ),
                              ),
                              child: Row(
                                children: [
                                  Icon(
                                    Icons.receipt_long,
                                    color: Colors.white,
                                    size: 32,
                                  ),
                                  SizedBox(width: 8),
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          'BIÊN LAI THANH TOÁN',
                                          style: TextStyle(
                                            fontSize: 18,
                                            fontWeight: FontWeight.bold,
                                            color: Colors.white,
                                          ),
                                        ),
                                        if (widget.payment.receiptNumber !=
                                            null)
                                          Text(
                                            'Số: ${widget.payment.receiptNumber}',
                                            style: TextStyle(
                                              fontSize: 14,
                                              color: Colors.white,
                                            ),
                                          ),
                                      ],
                                    ),
                                  ),
                                  Column(
                                    crossAxisAlignment: CrossAxisAlignment.end,
                                    children: [
                                      Text(
                                        'Ngày tạo:',
                                        style: TextStyle(
                                          fontSize: 12,
                                          color: Colors.white,
                                        ),
                                      ),
                                      Text(
                                        DateFormat(
                                          'dd/MM/yyyy',
                                        ).format(widget.payment.paymentDate),
                                        style: TextStyle(
                                          fontSize: 14,
                                          fontWeight: FontWeight.bold,
                                          color: Colors.white,
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),

                            // Merchant information
                            Container(
                              padding: EdgeInsets.all(16),
                              color: Colors.grey[100],
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'Smurf Company Rental',
                                    style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  SizedBox(height: 4),
                                  Text('123 Bike Street, Ho Chi Minh City'),
                                  Text('Mã số thuế: 0123456789'),
                                  Text('Điện thoại: (028) 1234 5678'),
                                ],
                              ),
                            ),

                            // Divider
                            Divider(height: 1, thickness: 1),

                            // Customer information
                            Padding(
                              padding: EdgeInsets.all(16),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'THÔNG TIN KHÁCH HÀNG',
                                    style: TextStyle(
                                      fontSize: 14,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.grey[700],
                                    ),
                                  ),
                                  SizedBox(height: 8),
                                  Row(
                                    children: [
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Row(
                                              children: [
                                                Text(
                                                  'Tên: ',
                                                  style: TextStyle(
                                                    fontWeight: FontWeight.bold,
                                                  ),
                                                ),
                                                Text(user!.name),
                                              ],
                                            ),
                                            SizedBox(height: 4),
                                            Row(
                                              children: [
                                                Text(
                                                  'Email: ',
                                                  style: TextStyle(
                                                    fontWeight: FontWeight.bold,
                                                  ),
                                                ),
                                                Expanded(
                                                  child: Text(
                                                    user!.email,
                                                    overflow:
                                                        TextOverflow.ellipsis,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ],
                                        ),
                                      ),
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Row(
                                              children: [
                                                Text(
                                                  'SĐT: ',
                                                  style: TextStyle(
                                                    fontWeight: FontWeight.bold,
                                                  ),
                                                ),
                                                Expanded(
                                                  child: Text(
                                                    user!.phone,
                                                    overflow:
                                                        TextOverflow.ellipsis,
                                                  ),
                                                ),
                                              ],
                                            ),
                                            SizedBox(height: 4),
                                            Row(
                                              children: [
                                                Text(
                                                  'Đơn thuê: ',
                                                  style: TextStyle(
                                                    fontWeight: FontWeight.bold,
                                                  ),
                                                ),
                                                Expanded(
                                                  child: Text(
                                                    '#${widget.payment.rentalId.substring(0, 8)}',
                                                    overflow:
                                                        TextOverflow.ellipsis,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),

                            // Payment details
                            Padding(
                              padding: EdgeInsets.all(16),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'CHI TIẾT THANH TOÁN',
                                    style: TextStyle(
                                      fontSize: 14,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.grey[700],
                                    ),
                                  ),
                                  SizedBox(height: 12),
                                  Container(
                                    decoration: BoxDecoration(
                                      border: Border.all(
                                        color: Colors.grey[300]!,
                                      ),
                                      borderRadius: BorderRadius.circular(4),
                                    ),
                                    child: Column(
                                      children: [
                                        // Payment items
                                        _buildPaymentItem(
                                          'Phí thuê xe',
                                          widget.payment.amount -
                                              (widget
                                                      .payment
                                                      .damageCompensation ??
                                                  0) -
                                              (widget.payment.lateFee ?? 0),
                                        ),

                                        if (widget.payment.lateFee != null &&
                                            widget.payment.lateFee! > 0)
                                          _buildPaymentItem(
                                            'Phí trả muộn (${widget.payment.lateHours} giờ)',
                                            widget.payment.lateFee!,
                                          ),

                                        if (widget.payment.damageCompensation !=
                                                null &&
                                            widget.payment.damageCompensation! >
                                                0)
                                          _buildPaymentItem(
                                            'Phí đền bù hư hại',
                                            widget.payment.damageCompensation!,
                                          ),

                                        // Total
                                        Container(
                                          padding: EdgeInsets.symmetric(
                                            horizontal: 16,
                                            vertical: 12,
                                          ),
                                          decoration: BoxDecoration(
                                            color: Colors.grey[100],
                                            border: Border(
                                              top: BorderSide(
                                                color: Colors.grey[300]!,
                                              ),
                                            ),
                                          ),
                                          child: Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            children: [
                                              Text(
                                                'Tổng cộng',
                                                style: TextStyle(
                                                  fontWeight: FontWeight.bold,
                                                  fontSize: 16,
                                                ),
                                              ),
                                              Text(
                                                '${NumberFormat('#,###').format(widget.payment.amount)} VND',
                                                style: TextStyle(
                                                  fontWeight: FontWeight.bold,
                                                  fontSize: 16,
                                                  color:
                                                      theme.colorScheme.primary,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),

                            // Payment method and transaction info
                            Padding(
                              padding: EdgeInsets.all(16),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'THÔNG TIN GIAO DỊCH',
                                    style: TextStyle(
                                      fontSize: 14,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.grey[700],
                                    ),
                                  ),
                                  SizedBox(height: 8),
                                  _buildInfoRow(
                                    'Phương thức',
                                    widget.payment.paymentMethod,
                                  ),
                                  _buildInfoRow(
                                    'Trạng thái',
                                    widget.payment.status,
                                  ),
                                  if (widget.payment.transactionId != null)
                                    _buildInfoRow(
                                      'Mã giao dịch',
                                      widget.payment.transactionId!,
                                    ),
                                  _buildInfoRow(
                                    'Thời gian',
                                    DateFormat(
                                      'dd/MM/yyyy HH:mm',
                                    ).format(widget.payment.paymentDate),
                                  ),
                                ],
                              ),
                            ),

                            // Signatures
                            Padding(
                              padding: EdgeInsets.all(16),
                              child: Row(
                                children: [
                                  Expanded(
                                    child: Column(
                                      children: [
                                        Text(
                                          'Người nhận',
                                          style: TextStyle(
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                        SizedBox(height: 40),
                                        Text('Smurf Company'),
                                      ],
                                    ),
                                  ),
                                  Expanded(
                                    child: Column(
                                      children: [
                                        Text(
                                          'Người thanh toán',
                                          style: TextStyle(
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                        SizedBox(height: 10),
                                        if (widget.payment.customerSignature !=
                                            null)
                                          SizedBox(
                                            height: 30,
                                            child: Image.memory(
                                              Uri.parse(
                                                widget
                                                    .payment
                                                    .customerSignature!,
                                              ).data!.contentAsBytes(),
                                              fit: BoxFit.contain,
                                            ),
                                          )
                                        else
                                          SizedBox(height: 30),
                                        Text(user!.name),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),

                            // Footer
                            Container(
                              padding: EdgeInsets.all(16),
                              decoration: BoxDecoration(
                                color: Colors.grey[100],
                                borderRadius: BorderRadius.only(
                                  bottomLeft: Radius.circular(8),
                                  bottomRight: Radius.circular(8),
                                ),
                              ),
                              child: Center(
                                child: Column(
                                  children: [
                                    Text(
                                      'Cảm ơn quý khách đã sử dụng dịch vụ!',
                                      style: TextStyle(
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    SizedBox(height: 4),
                                    Text(
                                      'Biên lai này là bằng chứng thanh toán hợp lệ.',
                                      style: TextStyle(
                                        fontSize: 12,
                                        color: Colors.grey[700],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),

                    SizedBox(height: 16),

                    ElevatedButton.icon(
                      onPressed: _captureAndShareReceipt,
                      icon: Icon(Icons.share),
                      label: Text('Chia sẻ biên lai'),
                      style: ElevatedButton.styleFrom(
                        foregroundColor: Colors.white,
                        padding: EdgeInsets.symmetric(
                          horizontal: 24,
                          vertical: 12,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
    );
  }

  Widget _buildPaymentItem(String title, double amount) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16, vertical: 10),
      decoration: BoxDecoration(
        border: Border(bottom: BorderSide(color: Colors.grey[200]!)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(title),
          Text('${NumberFormat('#,###').format(amount)} VND'),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.grey[700],
              ),
            ),
          ),
          Expanded(child: Text(value, style: TextStyle(color: Colors.black87))),
        ],
      ),
    );
  }
}
