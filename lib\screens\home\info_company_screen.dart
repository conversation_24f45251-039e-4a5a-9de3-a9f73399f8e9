// ignore_for_file: deprecated_member_use
import 'package:flutter/material.dart';
import 'package:panara_dialogs/panara_dialogs.dart';
import 'package:bike_rental_app/services/auth_service.dart';

class InfoCompanyScreen extends StatelessWidget {
  const InfoCompanyScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Scaffold(
      appBar: AppBar(
        title: Text('Thông tin công ty', style: theme.textTheme.titleLarge),
        centerTitle: true,
        backgroundColor: theme.appBarTheme.backgroundColor,
        elevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.appBarTheme.foregroundColor,
          ),
          onPressed: () {
            Navigator.pop(context);
          },
        ),
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            // Company Header
            Container(
              padding: EdgeInsets.symmetric(vertical: 32),
              decoration: BoxDecoration(
                color: theme.primaryColor,
                image: DecorationImage(
                  image: AssetImage('assets/images/company_bg.jpg'),
                  fit: BoxFit.cover,
                  colorFilter: ColorFilter.mode(
                    Colors.black.withOpacity(0.7),
                    BlendMode.darken,
                  ),
                ),
              ),
              child: Center(
                child: Column(
                  children: [
                    Container(
                      width: 120,
                      height: 120,
                      decoration: BoxDecoration(
                        color: theme.cardTheme.color,
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: theme.shadowColor.withOpacity(0.2),
                            spreadRadius: 2,
                            blurRadius: 5,
                          ),
                        ],
                      ),
                      child: ClipOval(
                        child: Image.asset(
                          'assets/images/company_logo.png',
                          fit: BoxFit.contain,
                        ),
                      ),
                    ),
                    SizedBox(height: 16),
                    Text(
                      'Làng Xì Trum',
                      style: theme.textTheme.headlineMedium?.copyWith(
                        color: theme.textTheme.headlineMedium?.color
                            ?.withOpacity(0.9),
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 8),
                    Text(
                      'Dịch vụ cho thuê xe máy',
                      style: theme.textTheme.titleLarge?.copyWith(
                        color: theme.textTheme.titleLarge?.color?.withOpacity(
                          0.7,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),

            Padding(
              padding: EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildCompanyInfo(theme),
                  SizedBox(height: 24),
                  _buildContactInfo(theme),
                  SizedBox(height: 24),
                  _buildBusinessHours(theme),
                  SizedBox(height: 32),
                  _buildLogoutButton(context, theme),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCompanyInfo(ThemeData theme) {
    return Card(
      elevation: theme.cardTheme.elevation,
      shape: theme.cardTheme.shape,
      color: theme.cardTheme.color,
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Center(
              child: Text(
                'GIỚI THIỆU',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            SizedBox(height: 12),
            Text(
              'Làng Xì Trum là đơn vị cho thuê xe máy uy tín hàng đầu tại Việt Nam. '
              'Chúng tôi tự hào cung cấp dịch vụ chất lượng cao với đội xe đời mới, '
              'được bảo dưỡng định kỳ và giá cả cạnh tranh.',
              style: theme.textTheme.bodyMedium?.copyWith(height: 1.5),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContactInfo(ThemeData theme) {
    return Card(
      elevation: theme.cardTheme.elevation,
      shape: theme.cardTheme.shape,
      color: theme.cardTheme.color,
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'THÔNG TIN LIÊN HỆ',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 16),
            _buildInfoRow(
              Icons.location_on,
              'Địa chỉ',
              '140 Đ.Lê Trọng Tấn, Q.Tân Phú',
              theme,
            ),
            _buildInfoRow(Icons.phone, 'Hotline', '1900 1234', theme),
            _buildInfoRow(
              Icons.email,
              'Email',
              '<EMAIL>',
              theme,
            ),
            _buildInfoRow(
              Icons.language,
              'Website',
              'www.langxitrum.com',
              theme,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBusinessHours(ThemeData theme) {
    return Card(
      elevation: theme.cardTheme.elevation,
      shape: theme.cardTheme.shape,
      color: theme.cardTheme.color,
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'GIỜ LÀM VIỆC',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('Thứ 2 - Thứ 6', style: theme.textTheme.bodyMedium),
                      Text('Thứ 7', style: theme.textTheme.bodyMedium),
                      Text('Chủ nhật', style: theme.textTheme.bodyMedium),
                    ],
                  ),
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text('7:00 - 22:00', style: theme.textTheme.bodyMedium),
                    Text('8:00 - 21:00', style: theme.textTheme.bodyMedium),
                    Text('8:00 - 20:00', style: theme.textTheme.bodyMedium),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(
    IconData icon,
    String title,
    String value,
    ThemeData theme,
  ) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Icon(icon, color: theme.primaryColor),
          SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          Text(value, style: theme.textTheme.bodyMedium),
        ],
      ),
    );
  }

  Widget _buildLogoutButton(BuildContext context, ThemeData theme) {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton.icon(
        icon: Icon(Icons.logout),
        label: Text('Đăng xuất'),
        style: theme.elevatedButtonTheme.style?.copyWith(
          padding: MaterialStateProperty.all(
            EdgeInsets.symmetric(vertical: 16),
          ),
          shape: MaterialStateProperty.all(
            RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          ),
          backgroundColor: MaterialStateProperty.all(theme.colorScheme.error),
          foregroundColor: MaterialStateProperty.all(theme.colorScheme.onError),
        ),
        onPressed: () {
          PanaraConfirmDialog.show(
            context,
            title: "Đăng xuất",
            message: "Bạn có chắc chắn muốn đăng xuất?",
            confirmButtonText: "Đăng xuất",
            cancelButtonText: "Hủy",
            textColor: theme.textTheme.bodyLarge!.color!,
            onTapCancel: () {
              Navigator.pop(context);
            },
            onTapConfirm: () async {
              final authService = AuthService();
              await authService.signOut();
              Navigator.pushNamedAndRemoveUntil(
                context,
                '/login',
                (route) => false,
              );
            },
            panaraDialogType: PanaraDialogType.custom,
            color: theme.primaryColor,
            barrierDismissible: false,
          );
        },
      ),
    );
  }
}
