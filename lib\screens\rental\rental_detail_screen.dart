import 'package:awesome_snackbar_content/awesome_snackbar_content.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../models/bike.dart';
import '../../models/rental.dart';
import '../../models/user.dart';
import '../../services/bike_service.dart';
import '../../services/rental_service.dart';
import '../../services/user_service.dart';
import '../payment/create_payment_screen.dart';
import 'map/bike_location_map_screen.dart';

class RentalDetailScreen extends StatefulWidget {
  final String rentalId;

  const RentalDetailScreen({super.key, required this.rentalId});

  @override
  State<RentalDetailScreen> createState() => _RentalDetailScreenState();
}

class _RentalDetailScreenState extends State<RentalDetailScreen> {
  final RentalService _rentalService = RentalService();
  final BikeService _bikeService = BikeService();
  final UserService _userService = UserService();
  bool _isLoading = true;
  Rental? _rental;
  Bike? _bike;
  User? _user;

  @override
  void initState() {
    super.initState();
    _loadRentalDetails();
  }

  Future<void> _loadRentalDetails() async {
    try {
      setState(() {
        _isLoading = true;
      });

      final rental = await _rentalService.getRentalById(widget.rentalId);
      final bike = await _bikeService.getBikeById(rental.bikeId);
      final user = await _userService.getUserById(rental.userId);

      setState(() {
        _rental = rental;
        _bike = bike;
        _user = user;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        final snackBar = SnackBar(
          elevation: 0,
          backgroundColor: Colors.transparent,
          behavior: SnackBarBehavior.floating,
          content: AwesomeSnackbarContent(
            contentType: ContentType.failure,
            title: 'Lỗi',
            message: 'Không thể tải thông tin đơn thuê',
          ),
        );
        ScaffoldMessenger.of(context)
          ..hideCurrentSnackBar()
          ..showSnackBar(snackBar);
      }
    }
  }

  Widget _buildInfoRow(String label, String value) {
    final theme = Theme.of(context);
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 140,
            child: Text(
              '$label:',
              style: theme.textTheme.bodyLarge?.copyWith(
                color: theme.textTheme.bodySmall?.color,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: theme.textTheme.bodyLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text('Chi tiết đơn thuê'),
        bottom: PreferredSize(
          preferredSize: Size.fromHeight(1.0),
          child: Container(
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(
                  color: theme.primaryColor.withOpacity(0.2),
                  width: 1.0,
                ),
              ),
            ),
          ),
        ),
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : _rental == null
              ? const Center(child: Text('Không tìm thấy thông tin đơn thuê'))
              : RefreshIndicator(
                onRefresh: _loadRentalDetails,
                child: SingleChildScrollView(
                  physics: const AlwaysScrollableScrollPhysics(),
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: theme.cardColor,
                          borderRadius: BorderRadius.circular(12),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.1),
                              blurRadius: 10,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  'Đơn thuê ${_rental!.id.substring(0, 8)}',
                                  style: theme.textTheme.titleLarge?.copyWith(
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 12,
                                    vertical: 6,
                                  ),
                                  decoration: BoxDecoration(
                                    color: _getStatusColor(
                                      _rental!.status,
                                    ).withOpacity(0.2),
                                    borderRadius: BorderRadius.circular(20),
                                  ),
                                  child: Text(
                                    _rental!.status,
                                    style: theme.textTheme.bodyMedium?.copyWith(
                                      color: _getStatusColor(_rental!.status),
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            const Divider(height: 32),
                            // Thông tin xe máy
                            Text(
                              'Thông tin xe máy',
                              style: theme.textTheme.titleMedium?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: theme.primaryColor,
                              ),
                            ),
                            const SizedBox(height: 8),
                            _buildInfoRow(
                              'Tên xe',
                              _bike?.name ?? 'Đang tải...',
                            ),
                            _buildInfoRow(
                              'Biển số',
                              _bike?.licensePlate ?? 'Đang tải...',
                            ),
                            _buildInfoRow(
                              'Loại xe',
                              _bike?.type ?? 'Đang tải...',
                            ),
                            _buildInfoRow(
                              'Giá thuê',
                              _bike != null
                                  ? NumberFormat.currency(
                                    locale: 'vi_VN',
                                    symbol: 'đ',
                                  ).format(_bike!.price)
                                  : 'Đang tải...',
                            ),

                            const Divider(height: 24),

                            // Thông tin người thuê
                            Text(
                              'Thông tin người thuê',
                              style: theme.textTheme.titleMedium?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: theme.primaryColor,
                              ),
                            ),
                            const SizedBox(height: 8),
                            _buildInfoRow(
                              'Tên người thuê',
                              _user?.name ?? 'Đang tải...',
                            ),
                            _buildInfoRow(
                              'Số điện thoại',
                              _user?.phone ?? 'Đang tải...',
                            ),
                            _buildInfoRow(
                              'Email',
                              _user?.email ?? 'Đang tải...',
                            ),
                            _buildInfoRow(
                              'Địa chỉ',
                              _user?.address ?? 'Đang tải...',
                            ),
                            _buildInfoRow(
                              'CCCD',
                              _user?.idCard ?? 'Đang tải...',
                            ),

                            const Divider(height: 24),

                            // Thông tin đơn thuê
                            Text(
                              'Thông tin đơn thuê',
                              style: theme.textTheme.titleMedium?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: theme.primaryColor,
                              ),
                            ),
                            const SizedBox(height: 8),
                            _buildInfoRow(
                              'Số lượng',
                              _rental!.quantity.toString(),
                            ),
                            _buildInfoRow(
                              'Ngày tạo',
                              DateFormat(
                                'dd/MM/yyyy',
                              ).format(_rental!.createdAt),
                            ),
                            _buildInfoRow(
                              'Tổng tiền',
                              NumberFormat.currency(
                                locale: 'vi_VN',
                                symbol: 'đ',
                              ).format(_rental!.totalAmount),
                            ),
                            _buildInfoRow(
                              'Ngày bắt đầu',
                              DateFormat(
                                'dd/MM/yyyy',
                              ).format(_rental!.startTime),
                            ),
                            _buildInfoRow(
                              'Ngày kết thúc',
                              DateFormat('dd/MM/yyyy').format(_rental!.endTime),
                            ),
                            if (_rental!.returnedDate != null)
                              _buildInfoRow(
                                'Ngày trả',
                                DateFormat(
                                  'dd/MM/yyyy',
                                ).format(_rental!.returnedDate!),
                              ),
                            if (_rental!.cancelledAt != null)
                              _buildInfoRow(
                                'Ngày hủy',
                                DateFormat(
                                  'dd/MM/yyyy HH:mm',
                                ).format(_rental!.cancelledAt!),
                              ),
                            if (_rental!.status == 'Ongoing' ||
                                _rental!.status == 'Expired')
                              Padding(
                                padding: const EdgeInsets.only(top: 16),
                                child: Column(
                                  children: [
                                    // Nút hủy đơn thuê (chỉ hiển thị khi đơn đang Ongoing và trong vòng 1 giờ)
                                    if (_rental!.status == 'Ongoing' &&
                                        _canCancelRental())
                                      Padding(
                                        padding: const EdgeInsets.only(
                                          bottom: 16,
                                        ),
                                        child: ElevatedButton.icon(
                                          icon: const Icon(
                                            Icons.cancel,
                                            size: 20,
                                          ),
                                          label: const Text('Hủy đơn thuê'),
                                          style: ElevatedButton.styleFrom(
                                            backgroundColor:
                                                Colors.red.shade700,
                                            foregroundColor: Colors.white,
                                            padding: const EdgeInsets.symmetric(
                                              vertical: 12,
                                            ),
                                            minimumSize: const Size.fromHeight(
                                              50,
                                            ),
                                          ),
                                          onPressed: _cancelRental,
                                        ),
                                      ),
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      mainAxisSize: MainAxisSize.max,
                                      children: [
                                        Expanded(
                                          child: ElevatedButton.icon(
                                            icon: const Icon(
                                              Icons.location_on,
                                              size: 20,
                                            ),
                                            label: const Text('Định vị'),
                                            style: ElevatedButton.styleFrom(
                                              backgroundColor: Colors.red,
                                              foregroundColor: Colors.white,
                                              padding:
                                                  const EdgeInsets.symmetric(
                                                    vertical: 12,
                                                  ),
                                              minimumSize:
                                                  const Size.fromHeight(50),
                                            ),
                                            onPressed:
                                                () => Navigator.push(
                                                  context,
                                                  MaterialPageRoute(
                                                    builder:
                                                        (context) =>
                                                            BikeLocationMapScreen(
                                                              bikeId:
                                                                  _rental!
                                                                      .bikeId,
                                                            ),
                                                  ),
                                                ),
                                          ),
                                        ),
                                        const SizedBox(width: 8),
                                        Expanded(
                                          child: ElevatedButton.icon(
                                            icon: const Icon(
                                              Icons.payment,
                                              size: 20,
                                            ),
                                            label: const Text('Thanh toán'),
                                            style: ElevatedButton.styleFrom(
                                              backgroundColor: Colors.green,
                                              foregroundColor: Colors.white,
                                              padding:
                                                  const EdgeInsets.symmetric(
                                                    vertical: 12,
                                                  ),
                                              minimumSize:
                                                  const Size.fromHeight(50),
                                            ),
                                            onPressed:
                                                () => Navigator.push(
                                                  context,
                                                  MaterialPageRoute(
                                                    builder:
                                                        (_) =>
                                                            CreatePaymentScreen(
                                                              rentalId:
                                                                  _rental!.id,
                                                            ),
                                                  ),
                                                ).then(
                                                  (_) => _loadRentalDetails(),
                                                ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                              ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
    );
  }

  // Kiểm tra xem đơn thuê có thể hủy không (trong vòng 30 phút từ khi tạo)
  bool _canCancelRental() {
    if (_rental == null || _rental!.status != 'Ongoing') {
      return false;
    }

    final now = DateTime.now();
    final difference = now.difference(_rental!.createdAt);
    return difference.inMinutes < 30;
  }

  // Hủy đơn thuê
  Future<void> _cancelRental() async {
    try {
      // Hiển thị hộp thoại xác nhận
      bool confirm =
          await showDialog(
            context: context,
            builder:
                (context) => AlertDialog(
                  title: Text('Xác nhận hủy đơn'),
                  content: Text(
                    'Bạn có chắc chắn muốn hủy đơn thuê này không?',
                  ),
                  actions: [
                    TextButton(
                      onPressed: () => Navigator.of(context).pop(false),
                      child: Text('Hủy'),
                    ),
                    TextButton(
                      onPressed: () => Navigator.of(context).pop(true),
                      child: Text('Xác nhận'),
                    ),
                  ],
                ),
          ) ??
          false;

      if (!confirm) return;

      // Hiển thị loading
      setState(() {
        _isLoading = true;
      });

      // Gọi API hủy đơn thuê
      await _rentalService.cancelRental(_rental!.id);

      // Tải lại thông tin đơn thuê
      await _loadRentalDetails();

      // Hiển thị thông báo thành công
      if (mounted) {
        final snackBar = SnackBar(
          elevation: 0,
          backgroundColor: Colors.transparent,
          behavior: SnackBarBehavior.floating,
          content: AwesomeSnackbarContent(
            contentType: ContentType.success,
            title: 'Thành công',
            message: 'Đơn thuê đã được hủy thành công',
          ),
        );
        ScaffoldMessenger.of(context)
          ..hideCurrentSnackBar()
          ..showSnackBar(snackBar);
      }
    } catch (e) {
      // Tắt loading
      setState(() {
        _isLoading = false;
      });

      // Hiển thị thông báo lỗi
      if (mounted) {
        final snackBar = SnackBar(
          elevation: 0,
          backgroundColor: Colors.transparent,
          behavior: SnackBarBehavior.floating,
          content: AwesomeSnackbarContent(
            contentType: ContentType.failure,
            title: 'Lỗi',
            message: 'Không thể hủy đơn thuê: ${e.toString()}',
          ),
        );
        ScaffoldMessenger.of(context)
          ..hideCurrentSnackBar()
          ..showSnackBar(snackBar);
      }
    }
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'Ongoing':
        return Colors.orange;
      case 'Completed':
        return Colors.green;
      case 'Expired':
        return Colors.red;
      case 'Cancelled':
        return Colors.grey;
      default:
        return Colors.grey;
    }
  }
}
