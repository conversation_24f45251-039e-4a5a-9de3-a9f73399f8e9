class Bike {
  String id;
  String name;
  String brandId;
  String type;
  String licensePlate;
  int quantity;
  double price;
  String status;
  String? imageUrl;

  Bike({
    required this.id,
    required this.name,
    required this.brandId,
    required this.type,
    required this.licensePlate,
    required this.quantity,
    required this.price,
    required this.status,
    this.imageUrl = '',
  });

  factory Bike.fromJson(Map<String, dynamic> json) {
    return Bike(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      brandId: json['brandId'] ?? '',
      type: json['type'] ?? '',
      licensePlate: json['licensePlate'] ?? '',
      quantity:
          json['quantity'] != null ? (json['quantity'] as num).toInt() : 0,
      price: json['price'] != null ? (json['price'] as num).toDouble() : 0.0,
      status: json['status'] ?? '',
      imageUrl: json['imageUrl'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'brandId': brandId,
      'type': type,
      'licensePlate': licensePlate,
      'quantity': quantity,
      'price': price,
      'status': status,
      'imageUrl': imageUrl,
    };
  }
}
