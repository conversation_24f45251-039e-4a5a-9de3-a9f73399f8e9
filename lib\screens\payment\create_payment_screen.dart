import 'package:awesome_snackbar_content/awesome_snackbar_content.dart';
import 'package:bike_rental_app/models/payment.dart';
import 'package:bike_rental_app/models/rental.dart';
import 'package:bike_rental_app/models/bank_account.dart';
import 'package:bike_rental_app/screens/payment/payment_receipt_screen.dart';
import 'package:bike_rental_app/screens/payment/vnpay_qr_screen.dart';
// import 'package:bike_rental_app/screens/payment/bank_transfer_confirmation_screen.dart'; // Đã loại bỏ
// import card_payment_demo_screen.dart đã bỏ
import 'package:bike_rental_app/services/payment_service.dart';
import 'package:bike_rental_app/services/rental_service.dart';
import 'package:bike_rental_app/services/bank_account_service.dart';
import 'package:bike_rental_app/widgets/custom_text_form_field.dart';
import 'package:bike_rental_app/utils/loading_dialog.dart';
import 'package:flutter/material.dart';
import 'package:bike_rental_app/widgets/common_widgets.dart';
import 'package:qr_flutter/qr_flutter.dart';
// import 'package:uuid/uuid.dart'; // Đã loại bỏ vì không còn sử dụng
import 'dart:convert';
import 'package:flutter/services.dart';
import 'dart:ui' as ui;
import 'package:flutter_signature_pad/flutter_signature_pad.dart';

class CreatePaymentScreen extends StatefulWidget {
  final String? rentalId;

  CreatePaymentScreen({this.rentalId});

  @override
  _CreatePaymentScreenState createState() => _CreatePaymentScreenState();
}

class _CreatePaymentScreenState extends State<CreatePaymentScreen> {
  late TextEditingController _amountController;
  late TextEditingController _damageCompensationController;
  late TextEditingController _damageDescriptionController;
  late TextEditingController _lateFeeController;
  final PaymentService _paymentService = PaymentService();
  final RentalService _rentalService = RentalService();
  final _formKey = GlobalKey<FormState>();

  String? selectedRentalId;
  String paymentMethod = 'Tiền mặt';
  double amount = 0;
  double damageCompensation = 0;
  double lateFee = 0;
  int lateHours = 0;
  bool hasDamage = false;
  bool isLateReturn = false;
  List<Rental> rentals = [];
  bool isLoading = true;
  bool showPaymentDetails = false;
  bool isProcessingPayment =
      false; // Biến để kiểm soát trạng thái xử lý thanh toán
  bool showCustomerSignature = false;
  final GlobalKey<SignatureState> _signatureKey = GlobalKey<SignatureState>();
  String? customerSignature;
  bool paymentInProgress = false;
  bool paymentSuccess = false;
  String? paymentErrorMessage;

  // Biến để lưu thông tin tài khoản ngân hàng
  BankAccount? _bankAccount;
  bool _isLoadingBankAccount = false;

  // Phương thức để tải thông tin tài khoản ngân hàng
  Future<void> _loadBankAccount() async {
    if (_bankAccount != null) return; // Đã tải rồi thì không tải lại

    setState(() {
      _isLoadingBankAccount = true;
    });

    try {
      final bankAccountService = BankAccountService();
      final bankAccount = await bankAccountService.getDefaultBankAccount();

      setState(() {
        _bankAccount = bankAccount;
        _isLoadingBankAccount = false;
      });
    } catch (e) {
      setState(() {
        _isLoadingBankAccount = false;
      });
      print('Error loading bank account: $e');
    }
  }

  @override
  void initState() {
    super.initState();
    _amountController = TextEditingController(text: amount.toString());
    _damageCompensationController = TextEditingController(text: '0');
    _damageDescriptionController = TextEditingController();
    _lateFeeController = TextEditingController(text: '0');
    _initializeData();
  }

  @override
  void dispose() {
    _amountController.dispose();
    _damageCompensationController.dispose();
    _damageDescriptionController.dispose();
    _lateFeeController.dispose();
    super.dispose();
  }

  Future<void> _initializeData() async {
    await _loadRentals();
    if (widget.rentalId != null) {
      setState(() {
        selectedRentalId = widget.rentalId;
      });
      await _loadRentalDetails(widget.rentalId!);
    }
  }

  Future<void> _loadRentals() async {
    try {
      final loadedRentals = await _rentalService.getRentals();
      setState(() {
        rentals =
            loadedRentals
                .where(
                  (rental) =>
                      rental.status != 'Completed' &&
                      rental.status != 'Cancelled',
                )
                .toList();
        isLoading = false;
      });
    } catch (e) {
      setState(() {
        isLoading = false;
      });
      final snackBar = SnackBar(
        elevation: 0,
        behavior: SnackBarBehavior.floating,
        backgroundColor: Colors.transparent,
        content: AwesomeSnackbarContent(
          title: 'Lỗi!',
          message: 'Không thể tải danh sách đơn thuê',
          contentType: ContentType.failure,
        ),
      );
      ScaffoldMessenger.of(context)
        ..hideCurrentSnackBar()
        ..showSnackBar(snackBar);
    }
  }

  Future<void> _loadRentalDetails(String rentalId) async {
    try {
      final rental = await _rentalService.getRentalById(rentalId);
      setState(() {
        // Lưu số tiền cơ bản vào biến amount và cập nhật controller
        amount = rental.totalAmount;
        _amountController.text = amount.toString(); // Update controller text

        // Kiểm tra nếu đã quá hạn thuê (so sánh thời gian hiện tại với thời gian kết thúc)
        final now = DateTime.now();
        if (now.isAfter(rental.endTime)) {
          isLateReturn = true;

          // Tính số giờ trả muộn
          final difference = now.difference(rental.endTime);
          lateHours = (difference.inMinutes / 60).ceil(); // Làm tròn lên số giờ

          // Tính phí phạt theo quy tắc
          double calculatedLateFee = 0;

          if (lateHours < 1) {
            // Dưới 1 giờ: Phí cố định 20.000 VNĐ
            calculatedLateFee = 20000;
          } else if (lateHours <= 12) {
            // Từ 1 giờ đến 12 giờ: 30.000 VNĐ/giờ
            calculatedLateFee = lateHours * 30000;
          } else {
            // Trên 12 giờ: Tính dựa trên tổng số giờ trả muộn
            // Tính số ngày trả muộn (làm tròn lên)
            int lateDays = (lateHours / 24).ceil();
            // Phí phạt = số ngày trả muộn * giá thuê một ngày
            calculatedLateFee = lateDays * rental.totalAmount;
          }

          lateFee = calculatedLateFee;
          _lateFeeController.text = lateFee.toString();
        } else {
          isLateReturn = false;
          lateFee = 0;
          lateHours = 0;
          _lateFeeController.text = '0';
        }

        // Cập nhật tổng tiền sau khi đã tính toán phí phạt
        _updateTotalAmount();
      });
    } catch (e) {
      final snackBar = SnackBar(
        elevation: 0,
        behavior: SnackBarBehavior.floating,
        backgroundColor: Colors.transparent,
        content: AwesomeSnackbarContent(
          title: 'Lỗi!',
          message: 'Không thể tải thông tin đơn thuê',
          contentType: ContentType.failure,
        ),
      );
      ScaffoldMessenger.of(context)
        ..hideCurrentSnackBar()
        ..showSnackBar(snackBar);
    }
  }

  void _updateTotalAmount() {
    setState(() {
      double baseAmount = double.tryParse(_amountController.text) ?? 0;
      double compensation =
          hasDamage
              ? (double.tryParse(_damageCompensationController.text) ?? 0)
              : 0;
      double latePayment =
          isLateReturn ? (double.tryParse(_lateFeeController.text) ?? 0) : 0;
      amount = baseAmount + compensation + latePayment;
    });
  }

  Widget _buildPaymentMethodDetails() {
    if (!showPaymentDetails) return SizedBox();

    switch (paymentMethod) {
      case 'Chuyển khoản':
        // Tải thông tin tài khoản ngân hàng nếu chưa tải
        if (_bankAccount == null && !_isLoadingBankAccount) {
          _loadBankAccount();
        }

        return Column(
          children: [
            SizedBox(height: 16),
            Container(
              padding: EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child:
                  _isLoadingBankAccount
                      ? Center(child: CircularProgressIndicator())
                      : _bankAccount == null
                      ? Center(
                        child: Text(
                          'Không thể tải thông tin tài khoản ngân hàng',
                          style: TextStyle(color: Colors.red),
                        ),
                      )
                      : Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Text(
                            'Quét mã QR để chuyển khoản',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                          ),
                          SizedBox(height: 16),
                          if (_bankAccount!.qrImageUrl.isNotEmpty)
                            Center(
                              child: ClipRRect(
                                borderRadius: BorderRadius.circular(8),
                                child: Image.network(
                                  _bankAccount!.qrImageUrl,
                                  height: 200,
                                  width: 200,
                                  fit: BoxFit.contain,
                                  loadingBuilder: (
                                    context,
                                    child,
                                    loadingProgress,
                                  ) {
                                    if (loadingProgress == null) return child;
                                    return SizedBox(
                                      height: 200,
                                      width: 200,
                                      child: Center(
                                        child: CircularProgressIndicator(
                                          value:
                                              loadingProgress
                                                          .expectedTotalBytes !=
                                                      null
                                                  ? loadingProgress
                                                          .cumulativeBytesLoaded /
                                                      loadingProgress
                                                          .expectedTotalBytes!
                                                  : null,
                                        ),
                                      ),
                                    );
                                  },
                                ),
                              ),
                            )
                          else
                            Center(
                              child: QrImageView(
                                data:
                                    'Chuyển khoản: ${amount.toStringAsFixed(0)} VND - Đơn thuê: $selectedRentalId',
                                version: QrVersions.auto,
                                size: 200,
                              ),
                            ),
                          SizedBox(height: 16),
                          Text(
                            'Thông tin chuyển khoản:',
                            style: TextStyle(fontWeight: FontWeight.bold),
                          ),
                          SizedBox(height: 8),
                          Text('Ngân hàng: ${_bankAccount!.bankName}'),
                          Text('Số tài khoản: ${_bankAccount!.accountNumber}'),
                          Text('Chủ tài khoản: ${_bankAccount!.accountName}'),
                          Text(
                            'Nội dung: Thanh toán_${selectedRentalId?.substring(0, 8)}',
                          ),
                        ],
                      ),
            ),
          ],
        );
      case 'VNPay/Ví điện tử':
        return Column(
          children: [
            SizedBox(height: 16),
            Container(
              padding: EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text(
                    'Thanh toán qua VNPay/Ví điện tử',
                    style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                  ),
                  SizedBox(height: 16),
                  Icon(Icons.qr_code_scanner, size: 80, color: Colors.blue),
                  SizedBox(height: 16),
                  Text(
                    'Khi bạn nhấn "Xác nhận thanh toán", hệ thống sẽ tạo mã QR để khách hàng quét và thanh toán.',
                    textAlign: TextAlign.center,
                    style: TextStyle(fontSize: 14),
                  ),
                ],
              ),
            ),
          ],
        );
      default:
        return SizedBox();
    }
  }

  // Phương thức _buildWalletOption đã bỏ vì không còn sử dụng

  Future<void> _getSignature() async {
    setState(() {
      showCustomerSignature = true;
    });
  }

  void _clearSignature() {
    final sign = _signatureKey.currentState;
    if (sign != null) {
      sign.clear();
      setState(() {
        customerSignature = null;
      });
    }
  }

  Future<void> _saveSignature() async {
    final sign = _signatureKey.currentState;
    if (sign != null && sign.hasPoints) {
      final image = await sign.getData();
      final bytes = await image.toByteData(format: ui.ImageByteFormat.png);
      if (bytes != null) {
        final encoded = base64Encode(bytes.buffer.asUint8List());
        setState(() {
          customerSignature = 'data:image/png;base64,$encoded';
          showCustomerSignature = false;
        });
      }
    } else {
      final snackBar = SnackBar(
        elevation: 0,
        behavior: SnackBarBehavior.floating,
        backgroundColor: Colors.transparent,
        content: AwesomeSnackbarContent(
          title: 'Chữ ký trống',
          message: 'Vui lòng ký vào ô trước khi lưu',
          contentType: ContentType.warning,
        ),
      );
      ScaffoldMessenger.of(context)
        ..hideCurrentSnackBar()
        ..showSnackBar(snackBar);
    }
  }

  Future<void> _processPayment() async {
    if (_formKey.currentState!.validate() && selectedRentalId != null) {
      // Kiểm tra xem có cần chữ ký không
      if (customerSignature == null) {
        // Hiển thị hộp thoại chữ ký
        setState(() {
          showCustomerSignature = true;
        });
        return;
      }

      // Set processing state
      setState(() {
        isProcessingPayment = true;
        paymentInProgress = true;
        paymentSuccess = false;
        paymentErrorMessage = null;
      });

      try {
        LoadingDialog.show(context);

        final rentalId = selectedRentalId!;
        final damageCompensation =
            hasDamage ? double.parse(_damageCompensationController.text) : null;
        final damageDescription =
            hasDamage ? _damageDescriptionController.text : null;
        final lateFeeValue =
            isLateReturn ? double.parse(_lateFeeController.text) : null;
        final lateHoursValue = isLateReturn ? lateHours : null;

        // Handle different payment methods
        switch (paymentMethod) {
          case 'VNPay/Ví điện tử':
            // Initialize VNPay payment with QR code
            final result = await _paymentService.initVNPayQRPayment(
              rentalId: rentalId,
              amount: amount,
              damageCompensation: damageCompensation,
              damageDescription: damageDescription,
              lateFee: lateFeeValue,
              lateHours: lateHoursValue,
              customerSignature: customerSignature,
            );

            if (mounted) LoadingDialog.hide(context);
            setState(() {
              isProcessingPayment = false;
              paymentInProgress = false;
            });

            // Navigate to VNPay QR Screen
            if (mounted) {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder:
                      (context) => VNPayQRScreen(
                        payment: result['payment'],
                        qrData: result['qrData'],
                      ),
                ),
              );
            }
            break;

          case 'Chuyển khoản':
            // Create bank transfer payment
            final payment = await _paymentService.createBankTransferPayment(
              rentalId: rentalId,
              amount: amount,
              damageCompensation: damageCompensation,
              damageDescription: damageDescription,
              lateFee: lateFeeValue,
              lateHours: lateHoursValue,
              customerSignature: customerSignature,
            );

            // Xử lý thanh toán với trạng thái "Completed" ngay lập tức
            final processedPayment = await _paymentService.processBankTransfer(
              paymentId: payment.id,
              bankTransferReference:
                  'STAFF-CONFIRMED-${DateTime.now().millisecondsSinceEpoch}',
              bankName: _bankAccount?.bankName ?? '',
              accountNumber: _bankAccount?.accountNumber ?? '',
              accountName: _bankAccount?.accountName ?? '',
              transferDate: DateTime.now(),
              transferNote: 'Thanh toán đã được xác nhận bởi nhân viên',
              confirmedBy: 'Staff',
            );

            if (mounted) LoadingDialog.hide(context);
            setState(() {
              isProcessingPayment = false;
              paymentInProgress = false;
              paymentSuccess = true;
            });

            // Hiển thị thông báo thành công
            if (mounted) {
              final snackBar = SnackBar(
                elevation: 0,
                behavior: SnackBarBehavior.floating,
                backgroundColor: Colors.transparent,
                content: AwesomeSnackbarContent(
                  title: 'Thanh toán thành công',
                  message:
                      'Thanh toán chuyển khoản đã được xác nhận thành công',
                  contentType: ContentType.success,
                ),
              );
              ScaffoldMessenger.of(context)
                ..hideCurrentSnackBar()
                ..showSnackBar(snackBar);

              // Chuyển đến màn hình biên lai
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder:
                      (context) =>
                          PaymentReceiptScreen(payment: processedPayment),
                ),
              ).then((_) {
                if (mounted) Navigator.pop(context);
              });
            }
            break;

          case 'Tiền mặt':
            // Process cash payment
            final payment = await _paymentService.addAndProcessPayment(
              rentalId: rentalId,
              paymentMethod: PaymentMethodConstants.cash,
              amount: amount,
              damageCompensation: damageCompensation,
              damageDescription: damageDescription,
              lateFee: lateFeeValue,
              lateHours: lateHoursValue,
              customerSignature: customerSignature,
            );

            if (mounted) LoadingDialog.hide(context);
            setState(() {
              isProcessingPayment = false;
              paymentInProgress = false;
              paymentSuccess =
                  payment.status == PaymentStatusConstants.completed;

              if (!paymentSuccess) {
                paymentErrorMessage = 'Thanh toán không thành công';
              }
            });

            if (mounted) {
              if (paymentSuccess) {
                final snackBar = SnackBar(
                  elevation: 0,
                  behavior: SnackBarBehavior.floating,
                  backgroundColor: Colors.transparent,
                  content: AwesomeSnackbarContent(
                    title: 'Thanh toán thành công',
                    message:
                        'Thanh toán thành công cho đơn thuê #${rentalId.substring(0, 8)}',
                    contentType: ContentType.success,
                  ),
                );
                ScaffoldMessenger.of(context)
                  ..hideCurrentSnackBar()
                  ..showSnackBar(snackBar);

                // Navigate to receipt screen
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder:
                        (context) => PaymentReceiptScreen(payment: payment),
                  ),
                ).then((_) {
                  if (mounted) Navigator.pop(context);
                });
              } else {
                final snackBar = SnackBar(
                  elevation: 0,
                  behavior: SnackBarBehavior.floating,
                  backgroundColor: Colors.transparent,
                  content: AwesomeSnackbarContent(
                    title: 'Thanh toán thất bại',
                    message:
                        paymentErrorMessage ??
                        'Đã xảy ra lỗi khi xử lý thanh toán',
                    contentType: ContentType.failure,
                  ),
                );
                ScaffoldMessenger.of(context)
                  ..hideCurrentSnackBar()
                  ..showSnackBar(snackBar);
              }
            }
            break;

          // Phương thức thẻ ngân hàng đã bị loại bỏ

          // Removed Ví điện tử case as it's now merged with VNPay

          default:
            if (mounted) LoadingDialog.hide(context);
            setState(() {
              isProcessingPayment = false;
              paymentInProgress = false;
            });

            if (mounted) {
              // Show error message for unsupported payment method
              final snackBar = SnackBar(
                elevation: 0,
                behavior: SnackBarBehavior.floating,
                backgroundColor: Colors.transparent,
                content: AwesomeSnackbarContent(
                  title: 'Phương thức không hỗ trợ',
                  message: 'Phương thức thanh toán này chưa được hỗ trợ',
                  contentType: ContentType.warning,
                ),
              );

              ScaffoldMessenger.of(context)
                ..hideCurrentSnackBar()
                ..showSnackBar(snackBar);
            }
            break;
        }
      } catch (e) {
        if (mounted) LoadingDialog.hide(context);
        setState(() {
          isProcessingPayment = false;
          paymentInProgress = false;
          paymentSuccess = false;
          paymentErrorMessage = e.toString();
        });

        if (mounted) {
          final snackBar = SnackBar(
            elevation: 0,
            behavior: SnackBarBehavior.floating,
            backgroundColor: Colors.transparent,
            content: AwesomeSnackbarContent(
              title: 'Lỗi!',
              message: 'Không thể xử lý thanh toán: ${e.toString()}',
              contentType: ContentType.failure,
            ),
          );
          ScaffoldMessenger.of(context)
            ..hideCurrentSnackBar()
            ..showSnackBar(snackBar);
        }
      }
    }
  }

  Widget _buildSignaturePad() {
    return Container(
      margin: EdgeInsets.symmetric(vertical: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: Colors.grey.shade400),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Text(
            'Chữ ký của khách hàng',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16,
              color: Colors.black87,
            ),
          ),
          Container(
            height: 200,
            decoration: BoxDecoration(
              border: Border(
                top: BorderSide(color: Colors.grey.shade300, width: 1),
              ),
            ),
            child: Signature(
              key: _signatureKey,
              color: Colors.black,
              strokeWidth: 3.0,
              backgroundPainter: null,
            ),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              TextButton.icon(
                onPressed: _clearSignature,
                icon: Icon(Icons.refresh),
                label: Text('Xoá'),
              ),
              TextButton.icon(
                onPressed: _saveSignature,
                icon: Icon(Icons.check),
                label: Text('Lưu chữ ký'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentStatus() {
    return Container(
      margin: EdgeInsets.symmetric(vertical: 16),
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: paymentSuccess ? Colors.green.shade50 : Colors.red.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: paymentSuccess ? Colors.green.shade300 : Colors.red.shade300,
        ),
      ),
      child: Column(
        children: [
          Icon(
            paymentSuccess ? Icons.check_circle : Icons.error,
            color: paymentSuccess ? Colors.green : Colors.red,
            size: 48,
          ),
          SizedBox(height: 8),
          Text(
            paymentSuccess ? 'Thanh toán thành công!' : 'Thanh toán thất bại',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 18,
              color: paymentSuccess ? Colors.green : Colors.red,
            ),
          ),
          SizedBox(height: 8),
          Text(
            paymentSuccess
                ? 'Cảm ơn bạn đã hoàn thành thanh toán'
                : paymentErrorMessage ?? 'Đã xảy ra lỗi khi xử lý thanh toán',
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 8),
          if (paymentSuccess)
            ElevatedButton.icon(
              onPressed: () {
                // Reset form for new payment
                setState(() {
                  paymentSuccess = false;
                  paymentInProgress = false;
                  customerSignature = null;
                  _clearSignature();
                });
              },
              icon: Icon(Icons.refresh),
              label: Text('Tạo thanh toán mới'),
            )
          else
            ElevatedButton.icon(
              onPressed: _processPayment,
              icon: Icon(Icons.replay),
              label: Text('Thử lại'),
            ),
        ],
      ),
    );
  }

  Widget _buildCustomerSignatureSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(height: 16),
        Text(
          'Chữ ký khách hàng',
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
        ),
        SizedBox(height: 8),

        if (customerSignature != null)
          Container(
            width: double.infinity,
            height: 100,
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Stack(
              children: [
                Center(
                  child: Image.memory(
                    Uri.parse(customerSignature!).data!.contentAsBytes(),
                    fit: BoxFit.contain,
                  ),
                ),
                Positioned(
                  top: 0,
                  right: 0,
                  child: IconButton(
                    icon: Icon(Icons.refresh, color: Colors.red),
                    onPressed: () {
                      setState(() {
                        customerSignature = null;
                      });
                    },
                  ),
                ),
              ],
            ),
          )
        else
          InkWell(
            onTap: _getSignature,
            child: Container(
              width: double.infinity,
              height: 100,
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.shade300),
                borderRadius: BorderRadius.circular(8),
                color: Colors.grey.shade100,
              ),
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.draw, size: 32, color: Colors.grey.shade600),
                    SizedBox(height: 8),
                    Text(
                      'Nhấn để thêm chữ ký',
                      style: TextStyle(color: Colors.grey.shade600),
                    ),
                  ],
                ),
              ),
            ),
          ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Scaffold(
      appBar: AppBar(
        bottom: PreferredSize(
          preferredSize: Size.fromHeight(1.0),
          child: Container(
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(
                  color: Theme.of(context).primaryColor.withOpacity(0.2),
                  width: 1.0,
                ),
              ),
            ),
          ),
        ),
        title: Text(
          'Tạo thanh toán mới',
          style: theme.appBarTheme.titleTextStyle,
        ),
        backgroundColor: theme.appBarTheme.backgroundColor,
        elevation: 0,
      ),
      body: Stack(
        children: [
          Container(
            color: theme.scaffoldBackgroundColor,
            child:
                isLoading
                    ? Center(
                      child: AppLoadingIndicator(
                        color: theme.progressIndicatorTheme.color!,
                        size: 30,
                      ),
                    )
                    : rentals.isEmpty
                    ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.info_outline,
                            size: 80,
                            color: theme.dividerColor,
                          ),
                          SizedBox(height: 16),
                          Text(
                            'Không có đơn thuê nào cần thanh toán',
                            style: theme.textTheme.bodyLarge,
                          ),
                        ],
                      ),
                    )
                    : Padding(
                      padding: EdgeInsets.all(16.0),
                      child: Form(
                        key: _formKey,
                        child: SingleChildScrollView(
                          child: Card(
                            elevation: theme.cardTheme.elevation,
                            shape: theme.cardTheme.shape,
                            color: theme.cardTheme.color,
                            child: Padding(
                              padding: EdgeInsets.all(20),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  if (paymentInProgress &&
                                      !paymentSuccess &&
                                      paymentErrorMessage == null)
                                    Center(
                                      child: Column(
                                        children: [
                                          AppLoadingIndicator(
                                            color: theme.colorScheme.primary,
                                            size: 40,
                                            type:
                                                LoadingIndicatorType
                                                    .staggeredDotsWave,
                                          ),
                                          SizedBox(height: 16),
                                          Text(
                                            'Đang xử lý thanh toán...',
                                            style: TextStyle(
                                              fontWeight: FontWeight.bold,
                                              fontSize: 16,
                                            ),
                                          ),
                                          SizedBox(height: 8),
                                          Text(
                                            'Vui lòng đợi trong khi chúng tôi xử lý thanh toán của bạn.',
                                            textAlign: TextAlign.center,
                                          ),
                                        ],
                                      ),
                                    )
                                  else if (paymentSuccess ||
                                      paymentErrorMessage != null)
                                    _buildPaymentStatus()
                                  else
                                    Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          'Thông tin thanh toán',
                                          style: theme.textTheme.titleLarge
                                              ?.copyWith(
                                                fontWeight: FontWeight.bold,
                                              ),
                                        ),
                                        SizedBox(height: 24),
                                        DropdownButtonFormField<String>(
                                          decoration: InputDecoration(
                                            labelText: 'Chọn đơn thuê',
                                            prefixIconColor:
                                                Theme.of(context).primaryColor,
                                            border: OutlineInputBorder(
                                              borderRadius:
                                                  BorderRadius.circular(10),
                                              borderSide: BorderSide(
                                                color: Theme.of(context)
                                                    .colorScheme
                                                    .primary
                                                    .withOpacity(0.5),
                                              ),
                                            ),
                                            enabledBorder: OutlineInputBorder(
                                              borderRadius:
                                                  BorderRadius.circular(10),
                                              borderSide: BorderSide(
                                                color: Theme.of(context)
                                                    .colorScheme
                                                    .primary
                                                    .withOpacity(0.5),
                                              ),
                                            ),
                                            focusedBorder: OutlineInputBorder(
                                              borderRadius:
                                                  BorderRadius.circular(10),
                                              borderSide: BorderSide(
                                                color:
                                                    Theme.of(
                                                      context,
                                                    ).colorScheme.primary,
                                                width: 2,
                                              ),
                                            ),
                                            prefixIcon: Icon(
                                              Icons.receipt_long,
                                            ),
                                          ),
                                          value: selectedRentalId,
                                          items:
                                              rentals.map((rental) {
                                                return DropdownMenuItem(
                                                  value: rental.id,
                                                  child: Text(
                                                    'Đơn thuê #${rental.id.substring(0, 8)}',
                                                    style: TextStyle(
                                                      fontWeight:
                                                          FontWeight.bold,
                                                      color:
                                                          Theme.of(
                                                            context,
                                                          ).colorScheme.primary,
                                                    ),
                                                  ),
                                                );
                                              }).toList(),
                                          onChanged: (value) {
                                            setState(() {
                                              selectedRentalId = value;
                                              if (value != null) {
                                                _loadRentalDetails(value);
                                              }
                                            });
                                          },
                                          validator:
                                              (value) =>
                                                  value == null
                                                      ? 'Vui lòng chọn đơn thuê'
                                                      : null,
                                        ),
                                        SizedBox(height: 16),
                                        CustomTextFormField(
                                          controller: _amountController,
                                          label: 'Số tiền cơ bản',
                                          prefixIcon: Icons.attach_money,
                                          keyboardType: TextInputType.number,
                                          onChanged: (val) {
                                            _updateTotalAmount();
                                          },
                                          validator: (val) {
                                            if (val == null || val.isEmpty) {
                                              return 'Vui lòng nhập số tiền';
                                            }
                                            if (double.tryParse(val) == null ||
                                                double.parse(val) <= 0) {
                                              return 'Số tiền không hợp lệ';
                                            }
                                            return null;
                                          },
                                        ),
                                        SizedBox(height: 16),
                                        SwitchListTile(
                                          title: Text(
                                            'Xe bị hư hại cần đền bù',
                                            style: TextStyle(
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                          subtitle: Text(
                                            'Chọn nếu khách hàng cần thanh toán phí đền bù',
                                          ),
                                          value: hasDamage,
                                          activeColor: Colors.red,
                                          onChanged: (bool value) {
                                            setState(() {
                                              hasDamage = value;
                                              _updateTotalAmount();
                                            });
                                          },
                                        ),
                                        if (isLateReturn) ...[
                                          SizedBox(height: 16),
                                          Container(
                                            padding: EdgeInsets.all(12),
                                            decoration: BoxDecoration(
                                              color: Colors.orange.withOpacity(
                                                0.1,
                                              ),
                                              borderRadius:
                                                  BorderRadius.circular(12),
                                              border: Border.all(
                                                color: Colors.orange
                                                    .withOpacity(0.3),
                                              ),
                                            ),
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Row(
                                                  children: [
                                                    Icon(
                                                      Icons
                                                          .warning_amber_rounded,
                                                      color: Colors.orange,
                                                    ),
                                                    SizedBox(width: 8),
                                                    Text(
                                                      'Phí phạt trả muộn',
                                                      style: TextStyle(
                                                        fontWeight:
                                                            FontWeight.bold,
                                                        fontSize: 16,
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                                SizedBox(height: 8),
                                                Text(
                                                  'Số giờ trả muộn: $lateHours giờ',
                                                ),
                                                SizedBox(height: 4),
                                                Text(
                                                  'Quy định phí phạt:',
                                                  style: TextStyle(
                                                    fontWeight: FontWeight.w500,
                                                  ),
                                                ),
                                                Text(
                                                  '- Dưới 1 giờ: 20.000 VNĐ',
                                                ),
                                                Text(
                                                  '- Từ 1-12 giờ: 30.000 VNĐ/giờ',
                                                ),
                                                Text(
                                                  '- Trên 12 giờ: Tính theo số ngày trả muộn',
                                                ),
                                                SizedBox(height: 8),
                                                CustomTextFormField(
                                                  controller:
                                                      _lateFeeController,
                                                  label: 'Phí phạt trả muộn',
                                                  prefixIcon: Icons.timer_off,
                                                  suffixIcon: Text('VND'),
                                                  keyboardType:
                                                      TextInputType.number,
                                                  onChanged: (val) {
                                                    _updateTotalAmount();
                                                  },
                                                ),
                                              ],
                                            ),
                                          ),
                                        ],
                                        if (hasDamage) ...[
                                          SizedBox(height: 12),
                                          CustomTextFormField(
                                            controller:
                                                _damageCompensationController,
                                            label: 'Phí đền bù',
                                            prefixIcon: Icons.healing,
                                            suffixIcon: Text('VND'),
                                            keyboardType: TextInputType.number,
                                            onChanged: (val) {
                                              _updateTotalAmount();
                                            },
                                            validator: (val) {
                                              if (hasDamage) {
                                                if (val == null ||
                                                    val.isEmpty) {
                                                  return 'Vui lòng nhập phí đền bù';
                                                }
                                                if (double.tryParse(val) ==
                                                    null) {
                                                  return 'Phí đền bù không hợp lệ';
                                                }
                                              }
                                              return null;
                                            },
                                          ),
                                          SizedBox(height: 12),
                                          CustomTextFormField(
                                            controller:
                                                _damageDescriptionController,
                                            label: 'Mô tả hư hại',
                                            prefixIcon: Icons.description,
                                            maxLines: 3,
                                            validator: (val) {
                                              if (hasDamage &&
                                                  (val == null ||
                                                      val.isEmpty)) {
                                                return 'Vui lòng mô tả hư hại';
                                              }
                                              return null;
                                            },
                                          ),
                                        ],
                                        SizedBox(height: 16),
                                        Container(
                                          padding: EdgeInsets.all(12),
                                          decoration: BoxDecoration(
                                            color: Colors.blue.withOpacity(0.1),
                                            borderRadius: BorderRadius.circular(
                                              12,
                                            ),
                                          ),
                                          child: Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            children: [
                                              Text(
                                                'Tổng cộng:',
                                                style: TextStyle(
                                                  fontWeight: FontWeight.bold,
                                                  fontSize: 16,
                                                ),
                                              ),
                                              Text(
                                                '${amount.toStringAsFixed(0)} VND',
                                                style: TextStyle(
                                                  fontWeight: FontWeight.bold,
                                                  fontSize: 18,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                        SizedBox(height: 16),
                                        DropdownButtonFormField<String>(
                                          decoration: InputDecoration(
                                            labelText: 'Phương thức thanh toán',
                                            border: OutlineInputBorder(
                                              borderRadius:
                                                  BorderRadius.circular(10),
                                              borderSide: BorderSide(
                                                color: Theme.of(context)
                                                    .colorScheme
                                                    .primary
                                                    .withOpacity(0.5),
                                              ),
                                            ),
                                            enabledBorder: OutlineInputBorder(
                                              borderRadius:
                                                  BorderRadius.circular(10),
                                              borderSide: BorderSide(
                                                color: Theme.of(context)
                                                    .colorScheme
                                                    .primary
                                                    .withOpacity(0.5),
                                              ),
                                            ),
                                            focusedBorder: OutlineInputBorder(
                                              borderRadius:
                                                  BorderRadius.circular(10),
                                              borderSide: BorderSide(
                                                color:
                                                    Theme.of(
                                                      context,
                                                    ).colorScheme.primary,
                                                width: 2,
                                              ),
                                            ),
                                            prefixIcon: Icon(
                                              Icons.payment,
                                              color: theme.colorScheme.primary,
                                            ),
                                          ),
                                          value: paymentMethod,
                                          items:
                                              [
                                                'Tiền mặt',
                                                'Chuyển khoản',
                                                'VNPay/Ví điện tử',
                                              ].map((method) {
                                                return DropdownMenuItem(
                                                  value: method,
                                                  child: Text(
                                                    method,
                                                    style: TextStyle(
                                                      fontWeight:
                                                          FontWeight.bold,
                                                      color:
                                                          theme
                                                              .colorScheme
                                                              .primary,
                                                    ),
                                                  ),
                                                );
                                              }).toList(),
                                          onChanged: (value) {
                                            setState(() {
                                              paymentMethod = value!;
                                              showPaymentDetails =
                                                  value != 'Tiền mặt';
                                            });
                                          },
                                        ),
                                        _buildPaymentMethodDetails(),

                                        _buildCustomerSignatureSection(),

                                        SizedBox(height: 32),
                                        SizedBox(
                                          width: double.infinity,
                                          height: 50,
                                          child: ElevatedButton(
                                            style:
                                                isProcessingPayment
                                                    ? theme
                                                        .elevatedButtonTheme
                                                        .style
                                                        ?.copyWith(
                                                          backgroundColor:
                                                              MaterialStateProperty.all(
                                                                Colors.grey,
                                                              ),
                                                        )
                                                    : theme
                                                        .elevatedButtonTheme
                                                        .style,
                                            onPressed:
                                                isProcessingPayment
                                                    ? null
                                                    : _processPayment,
                                            child:
                                                isProcessingPayment
                                                    ? Row(
                                                      mainAxisAlignment:
                                                          MainAxisAlignment
                                                              .center,
                                                      children: [
                                                        AppLoadingIndicator(
                                                          color: Colors.black,
                                                          size: 20,
                                                          type:
                                                              LoadingIndicatorType
                                                                  .staggeredDotsWave,
                                                        ),
                                                        SizedBox(width: 12),
                                                        Text(
                                                          'Đang xử lý...',
                                                          style: TextStyle(
                                                            fontSize: 16,
                                                            color: Colors.black,
                                                          ),
                                                        ),
                                                      ],
                                                    )
                                                    : Text(
                                                      'Xác nhận thanh toán',
                                                      style: TextStyle(
                                                        fontSize: 16,
                                                      ),
                                                    ),
                                          ),
                                        ),
                                      ],
                                    ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
          ),

          if (showCustomerSignature)
            Container(
              color: Colors.black.withOpacity(0.5),
              child: Center(
                child: Container(
                  margin: EdgeInsets.all(20),
                  padding: EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        'Chữ ký xác nhận thanh toán',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(height: 16),
                      _buildSignaturePad(),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          ElevatedButton.icon(
                            onPressed: () {
                              setState(() {
                                showCustomerSignature = false;
                              });
                            },
                            icon: Icon(Icons.cancel),
                            label: Text('Huỷ'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.grey,
                            ),
                          ),
                          ElevatedButton.icon(
                            onPressed: _saveSignature,
                            icon: Icon(Icons.check),
                            label: Text('Xác nhận'),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }
}
