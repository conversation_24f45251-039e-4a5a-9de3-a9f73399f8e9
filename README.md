# Bike Rental App

Ứng dụng quản lý cho thuê xe máy dành cho nhân viên, <PERSON><PERSON><PERSON><PERSON> phát triển bằng Flutter và Firebase.

## Thông tin đồ án

**Đ<PERSON> án môn học:** Lập trình di động

**Trường:** <PERSON><PERSON><PERSON> học Công Thương TP.HCM

**Thành viên nhóm:**

- Trần Công Minh - 2001222641
- Lê Đức Trung - 2001225676
- <PERSON><PERSON><PERSON><PERSON> Chí Tài - 2001224227
- Tạ Nguyên Vũ - 2001225916

## Tổng quan

Bike Rental App là một ứng dụng di động được thiết kế để giúp nhân viên quản lý việc cho thuê xe máy. Ứng dụng cung cấp các tính năng quản lý xe, kh<PERSON><PERSON> hà<PERSON>, đơn thuê và thanh toán, cùng với các công cụ thống kê và báo cáo.

## <PERSON><PERSON>h năng chính

### Qu<PERSON>n lý xe

- <PERSON><PERSON><PERSON><PERSON>, s<PERSON><PERSON>, xóa thông tin xe máy
- Quản lý thương hiệu xe
- Theo dõi trạng thái và số lượng xe
- Xem vị trí xe trên bản đồ

### Quản lý khách hàng

- Thêm và quản lý thông tin khách hàng
- Lịch sử thuê xe của khách hàng
- Tìm kiếm khách hàng

### Quản lý đơn thuê

- Tạo đơn thuê mới
- Theo dõi trạng thái đơn thuê (Đang thuê, Hoàn thành, Hết hạn, Đã hủy)
- Quét mã QR để xác nhận đơn thuê
- Hủy đơn thuê trong vòng 30 phút

### Thanh toán

- Hỗ trợ nhiều phương thức thanh toán (Tiền mặt, Chuyển khoản, VNPay/Ví điện tử)
- Xử lý phí bồi thường hư hỏng và phí trễ hạn
- Gửi email xác nhận thanh toán
- In hóa đơn thanh toán

### Thống kê và báo cáo

- Thống kê doanh thu theo thời gian
- Phân tích theo phương thức thanh toán
- Biểu đồ trực quan hóa dữ liệu

### Quản lý hệ thống (Admin)

- Quản lý tài khoản nhân viên
- Phân quyền admin/nhân viên
- Quản lý thông tin công ty
- Quản lý tài khoản ngân hàng

### Tính năng khác

- Thông báo đẩy
- Chế độ tối/sáng
- Thiết kế responsive
- Tối ưu hiệu suất

## Công nghệ sử dụng

### Frontend

- Flutter SDK
- Provider (State Management)
- Crystal Navigation Bar
- Flutter Map
- Cached Network Image
- PDF & Printing
- QR Code Scanner & Generator
- Chart (FL Chart)

### Backend & Database

- Firebase Authentication
- Cloud Firestore
- Firebase Storage
- Firebase Cloud Functions

### Tích hợp

- Email Service (Mailer)
- Google Maps Navigation
- Thanh toán (VNPay, Chuyển khoản, Tiền mặt)

## Cài đặt

### Yêu cầu

- Flutter SDK (phiên bản 3.7.2 trở lên)
- Dart SDK
- Firebase project

### Các bước cài đặt

1. Clone repository

```bash
git clone https://github.com/yourusername/bike-rental-app.git
cd bike-rental-app
```

2. Cài đặt các dependencies

```bash
flutter pub get
```

3. Cấu hình Firebase

- Tạo project Firebase mới
- Thêm ứng dụng Android/iOS vào project Firebase
- Tải file cấu hình và thêm vào project
- Kích hoạt các dịch vụ Firebase cần thiết (Authentication, Firestore, Storage)

4. Tạo file .env trong thư mục gốc và thêm các biến môi trường cần thiết

5. Chạy ứng dụng

```bash
flutter run
```

## Tối ưu hiệu suất

Ứng dụng đã được tối ưu hóa để đảm bảo hiệu suất tốt nhất:

- Sử dụng IndexedStack và AutomaticKeepAliveClientMixin để tránh tải lại màn hình khi chuyển tab
- Tối ưu hóa tải hình ảnh với cached_network_image
- Lazy loading cho danh sách dài
- Phân trang dữ liệu từ Firestore
- Sử dụng const constructors và ListView.builder

## Đóng góp

Mọi đóng góp đều được hoan nghênh. Vui lòng tạo issue hoặc pull request để cải thiện ứng dụng.

## Giấy phép

Dự án này được phân phối dưới giấy phép MIT. Xem file `LICENSE` để biết thêm chi tiết.

## Liên hệ

Nếu bạn có bất kỳ câu hỏi hoặc góp ý nào về dự án, vui lòng liên hệ với chúng tôi qua email hoặc tạo issue trên GitHub repository.

Đây là đồ án phục vụ mục đích học tập và nghiên cứu tại Trường Đại học Công Thương TP.HCM.
