import 'package:bike_rental_app/models/payment.dart';
import 'package:bike_rental_app/models/rental.dart';
import 'package:bike_rental_app/services/rental_service.dart';
import 'package:bike_rental_app/services/user_service.dart';
import 'package:bike_rental_app/services/bike_service.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:loading_animation_widget/loading_animation_widget.dart';

class PaymentDetailScreen extends StatefulWidget {
  final Payment payment;

  const PaymentDetailScreen({super.key, required this.payment});

  @override
  _PaymentDetailScreenState createState() => _PaymentDetailScreenState();
}

class _PaymentDetailScreenState extends State<PaymentDetailScreen> {
  final RentalService _rentalService = RentalService();
  final UserService _userService = UserService();
  final BikeService _bikeService = BikeService();

  bool isLoading = true;
  Rental? rental;
  String userName = '';
  String bikeName = '';

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    try {
      // Lấy thông tin đơn thuê
      rental = await _rentalService.getRentalById(widget.payment.rentalId);

      // Lấy thông tin người dùng
      final user = await _userService.getUserById(rental!.userId);
      if (user != null) {
        userName = user.name;
      }

      // Lấy thông tin xe
      final bike = await _bikeService.getBikeById(rental!.bikeId);
      bikeName = bike.name;

      setState(() {
        isLoading = false;
      });
    } catch (e) {
      setState(() {
        isLoading = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Không thể tải thông tin: ${e.toString()}')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Chi tiết thanh toán',
          style: theme.textTheme.titleLarge?.copyWith(
            color: theme.colorScheme.primary,
          ),
        ),
        backgroundColor: theme.appBarTheme.backgroundColor,
        elevation: 2,
        bottom: PreferredSize(
          preferredSize: Size.fromHeight(1.0),
          child: Container(
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(
                  color: theme.colorScheme.primary.withOpacity(0.2),
                  width: 1.0,
                ),
              ),
            ),
          ),
        ),
        iconTheme: IconThemeData(color: theme.colorScheme.primary),
      ),
      body:
          isLoading
              ? Center(
                child: LoadingAnimationWidget.fourRotatingDots(
                  color: theme.colorScheme.primary,
                  size: 40,
                ),
              )
              : rental == null
              ? Center(child: Text('Không tìm thấy thông tin đơn thuê'))
              : SingleChildScrollView(
                padding: EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildPaymentInfoCard(theme),
                    SizedBox(height: 16),
                    _buildRentalInfoCard(theme),
                    SizedBox(height: 16),
                    if (widget.payment.damageCompensation != null) ...[
                      _buildDamageInfoCard(theme),
                      SizedBox(height: 16),
                    ],
                    if (widget.payment.lateFee != null) ...[
                      _buildLateFeeInfoCard(theme),
                      SizedBox(height: 16),
                    ],
                    _buildTotalAmountCard(theme),
                  ],
                ),
              ),
    );
  }

  Widget _buildPaymentInfoCard(ThemeData theme) {
    return Card(
      elevation: 3,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Thông tin thanh toán',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: _getStatusColor(
                      widget.payment.status,
                    ).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: _getStatusColor(widget.payment.status),
                      width: 1,
                    ),
                  ),
                  child: Text(
                    widget.payment.status,
                    style: TextStyle(
                      color: _getStatusColor(widget.payment.status),
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            Divider(height: 24),
            _buildInfoRow(
              'Mã thanh toán',
              '#${widget.payment.id.substring(0, 8)}',
            ),
            _buildInfoRow('Phương thức', widget.payment.paymentMethod),
            _buildInfoRow(
              'Ngày thanh toán',
              DateFormat('dd/MM/yyyy HH:mm').format(widget.payment.paymentDate),
            ),
            _buildInfoRow('Khách hàng', userName),
          ],
        ),
      ),
    );
  }

  Widget _buildRentalInfoCard(ThemeData theme) {
    return Card(
      elevation: 3,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Thông tin đơn thuê',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            Divider(height: 24),
            _buildInfoRow('Mã đơn thuê', '#${rental!.id.substring(0, 8)}'),
            _buildInfoRow('Xe thuê', bikeName),
            _buildInfoRow('Số lượng', rental!.quantity.toString()),
            _buildInfoRow(
              'Thời gian bắt đầu',
              DateFormat('dd/MM/yyyy HH:mm').format(rental!.startTime),
            ),
            _buildInfoRow(
              'Thời gian kết thúc',
              DateFormat('dd/MM/yyyy HH:mm').format(rental!.endTime),
            ),
            if (rental!.returnedDate != null)
              _buildInfoRow(
                'Thời gian trả xe',
                DateFormat('dd/MM/yyyy HH:mm').format(rental!.returnedDate!),
              ),
            if (rental!.cancelledAt != null)
              _buildInfoRow(
                'Thời gian hủy',
                DateFormat('dd/MM/yyyy HH:mm').format(rental!.cancelledAt!),
              ),
            _buildInfoRow(
              'Tiền thuê cơ bản',
              NumberFormat.currency(
                locale: 'vi_VN',
                symbol: 'đ',
              ).format(rental!.totalAmount),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDamageInfoCard(ThemeData theme) {
    return Card(
      elevation: 3,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.warning_amber_rounded, color: Colors.red),
                SizedBox(width: 8),
                Text(
                  'Thông tin hư hại',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            Divider(height: 24),
            _buildInfoRow(
              'Phí đền bù',
              NumberFormat.currency(
                locale: 'vi_VN',
                symbol: 'đ',
              ).format(widget.payment.damageCompensation),
            ),
            _buildInfoRow(
              'Mô tả hư hại',
              widget.payment.damageDescription ?? '',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLateFeeInfoCard(ThemeData theme) {
    return Card(
      elevation: 3,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.timer_off, color: Colors.orange),
                SizedBox(width: 8),
                Text(
                  'Phí phạt trả muộn',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            Divider(height: 24),
            _buildInfoRow('Số giờ trả muộn', '${widget.payment.lateHours} giờ'),
            _buildInfoRow(
              'Phí phạt',
              NumberFormat.currency(
                locale: 'vi_VN',
                symbol: 'đ',
              ).format(widget.payment.lateFee),
            ),
            SizedBox(height: 8),
            Text(
              'Quy định phí phạt:',
              style: TextStyle(fontWeight: FontWeight.w500),
            ),
            Text('- Dưới 1 giờ: 20.000 VNĐ'),
            Text('- Từ 1-12 giờ: 30.000 VNĐ/giờ'),
            Text('- Trên 12 giờ: Tính như một ngày thuê mới'),
          ],
        ),
      ),
    );
  }

  Widget _buildTotalAmountCard(ThemeData theme) {
    return Card(
      elevation: 3,
      color: theme.colorScheme.primary,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Tổng thanh toán',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.onPrimary,
              ),
            ),
            Divider(
              height: 24,
              color: theme.colorScheme.onPrimary.withOpacity(0.5),
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Tiền thuê cơ bản:',
                  style: TextStyle(
                    color: theme.colorScheme.onPrimary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  NumberFormat.currency(
                    locale: 'vi_VN',
                    symbol: 'đ',
                  ).format(rental!.totalAmount),
                  style: TextStyle(
                    color: theme.colorScheme.onPrimary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            if (widget.payment.damageCompensation != null) ...[
              SizedBox(height: 8),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Phí đền bù hư hại:',
                    style: TextStyle(
                      color: theme.colorScheme.onPrimary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  Text(
                    NumberFormat.currency(
                      locale: 'vi_VN',
                      symbol: 'đ',
                    ).format(widget.payment.damageCompensation),
                    style: TextStyle(
                      color: theme.colorScheme.onPrimary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ],
            if (widget.payment.lateFee != null) ...[
              SizedBox(height: 8),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Phí phạt trả muộn:',
                    style: TextStyle(
                      color: theme.colorScheme.onPrimary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  Text(
                    NumberFormat.currency(
                      locale: 'vi_VN',
                      symbol: 'đ',
                    ).format(widget.payment.lateFee),
                    style: TextStyle(
                      color: theme.colorScheme.onPrimary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ],
            Divider(
              height: 24,
              color: theme.colorScheme.onPrimary.withOpacity(0.5),
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Tổng cộng:',
                  style: TextStyle(
                    color: theme.colorScheme.onPrimary,
                    fontWeight: FontWeight.bold,
                    fontSize: 18,
                  ),
                ),
                Text(
                  NumberFormat.currency(
                    locale: 'vi_VN',
                    symbol: 'đ',
                  ).format(widget.payment.amount),
                  style: TextStyle(
                    color: theme.colorScheme.onPrimary,
                    fontWeight: FontWeight.bold,
                    fontSize: 18,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    final theme = Theme.of(context);
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurfaceVariant.withOpacity(0.7),
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: theme.textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'Completed':
        return Colors.green;
      case 'Pending':
        return Colors.orange;
      case 'Failed':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }
}
