import 'package:bike_rental_app/models/rental.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../services/user_service.dart';
import '../services/email_service.dart';

class RentalService {
  final CollectionReference rentalsCollection = FirebaseFirestore.instance
      .collection('rentals');

  // Lấy danh sách đơn thuê với các tùy chọn lọc
  Future<List<Rental>> getRentals({
    DateTime? startDate,
    DateTime? endDate,
    int limit = 100,
    String? lastDocumentId,
    String? status,
  }) async {
    try {
      Query query = rentalsCollection;

      // Áp dụng bộ lọc thời gian nếu có
      if (startDate != null) {
        query = query.where(
          'startTime',
          isGreaterThanOrEqualTo: Timestamp.fromDate(startDate),
        );
      }

      if (endDate != null) {
        query = query.where(
          'startTime',
          isLessThanOrEqualTo: Timestamp.fromDate(endDate),
        );
      }

      // Áp dụng bộ lọc trạng thái nếu có
      if (status != null) {
        query = query.where('status', isEqualTo: status);
      }

      // Sắp xếp theo thời gian bắt đầu giảm dần (mới nhất trước)
      query = query.orderBy('startTime', descending: true);

      // Áp dụng phân trang nếu có
      if (lastDocumentId != null) {
        DocumentSnapshot lastDoc =
            await rentalsCollection.doc(lastDocumentId).get();
        query = query.startAfterDocument(lastDoc);
      }

      // Giới hạn số lượng kết quả
      query = query.limit(limit);

      QuerySnapshot querySnapshot = await query.get();
      return querySnapshot.docs.map((doc) {
        Map<String, dynamic> data = doc.data() as Map<String, dynamic>;
        return Rental.fromMap(doc.id, data);
      }).toList();
    } catch (e) {
      print('Error getting rentals: $e');
      throw e;
    }
  }

  // Lấy tổng số đơn thuê theo khoảng thời gian
  Future<int> getRentalCount({
    DateTime? startDate,
    DateTime? endDate,
    String? status,
  }) async {
    try {
      Query query = rentalsCollection;

      // Áp dụng bộ lọc thời gian nếu có
      if (startDate != null) {
        query = query.where(
          'startTime',
          isGreaterThanOrEqualTo: Timestamp.fromDate(startDate),
        );
      }

      if (endDate != null) {
        query = query.where(
          'startTime',
          isLessThanOrEqualTo: Timestamp.fromDate(endDate),
        );
      }

      // Áp dụng bộ lọc trạng thái nếu có
      if (status != null) {
        query = query.where('status', isEqualTo: status);
      }

      AggregateQuerySnapshot aggregateSnapshot = await query.count().get();
      return aggregateSnapshot.count ?? 0;
    } catch (e) {
      print('Error getting rental count: $e');
      throw e;
    }
  }

  // Lấy thông tin đơn thuê theo ID
  Future<Rental> getRentalById(String id) async {
    try {
      DocumentSnapshot doc = await rentalsCollection.doc(id).get();
      if (doc.exists) {
        Map<String, dynamic> data = doc.data() as Map<String, dynamic>;
        return Rental.fromMap(doc.id, data);
      } else {
        throw Exception('Rental not found');
      }
    } catch (e) {
      print('Error getting rental: $e');
      throw e;
    }
  }

  // Thêm đơn thuê mới
  Future<void> addRental(Rental rental) async {
    try {
      await rentalsCollection.doc(rental.id).set(rental.toMap());

      // Gửi email xác nhận đơn thuê thành công
      try {
        final userService = UserService();
        final emailService = EmailService();

        // Lấy thông tin người dùng
        final user = await userService.getUserById(rental.userId);
        if (user != null) {
          // Gửi email xác nhận
          await emailService.sendRentalConfirmation(rental: rental, user: user);
        }
      } catch (emailError) {
        print('Error sending rental confirmation email: $emailError');
        // Không throw lỗi ở đây để không ảnh hưởng đến luồng chính
      }
    } catch (e) {
      print('Error adding rental: $e');
      throw e;
    }
  }

  // Cập nhật trạng thái đơn thuê
  Future<void> updateRentalStatus(String id, String status) async {
    try {
      await rentalsCollection.doc(id).update({'status': status});
    } catch (e) {
      print('Error updating rental status: $e');
      throw e;
    }
  }

  // Ghi nhận xe trả
  Future<void> recordBikeReturn(String id) async {
    try {
      // Lấy thông tin đơn thuê
      DocumentSnapshot rentalDoc = await rentalsCollection.doc(id).get();
      if (!rentalDoc.exists) {
        throw Exception('Rental not found');
      }

      Map<String, dynamic> rentalData =
          rentalDoc.data() as Map<String, dynamic>;
      String bikeId = rentalData['bikeId'];
      int rentedQuantity =
          rentalData['quantity'] ??
          1; // Số lượng đã thuê, mặc định là 1 nếu không có dữ liệu

      // Cập nhật trạng thái và ngày trả của đơn thuê
      await rentalsCollection.doc(id).update({
        'returnedDate': DateTime.now(),
        'status': 'Completed',
      });

      // Cập nhật trạng thái và số lượng xe trong collection 'bikes'
      await FirebaseFirestore.instance.collection('bikes').doc(bikeId).update({
        'isAvailable': true,
        'quantity': FieldValue.increment(rentedQuantity),
      });
    } catch (e) {
      print('Error recording bike return: $e');
      throw e;
    }
  }

  // Lấy danh sách đơn thuê của một người dùng
  Future<List<Map<String, dynamic>>> getUserRentals(String userId) async {
    try {
      QuerySnapshot querySnapshot =
          await rentalsCollection.where('userId', isEqualTo: userId).get();

      List<Map<String, dynamic>> rentals =
          querySnapshot.docs.map((doc) {
            Map<String, dynamic> data = doc.data() as Map<String, dynamic>;
            data['id'] = doc.id;
            return data;
          }).toList();

      // Sắp xếp danh sách theo thời gian bắt đầu (descending)
      rentals.sort((a, b) {
        Timestamp aTime = a['startTime'] as Timestamp;
        Timestamp bTime = b['startTime'] as Timestamp;
        return bTime.compareTo(aTime); // Descending order
      });

      return rentals;
    } catch (e) {
      print('Error getting user rentals: $e');
      throw e;
    }
  }

  // Hủy đơn thuê (chỉ cho phép hủy trong vòng 30 phút từ khi Ongoing)
  Future<void> cancelRental(String id) async {
    try {
      // Lấy thông tin đơn thuê
      DocumentSnapshot rentalDoc = await rentalsCollection.doc(id).get();
      if (!rentalDoc.exists) {
        throw Exception('Rental not found');
      }

      Map<String, dynamic> rentalData =
          rentalDoc.data() as Map<String, dynamic>;

      // Kiểm tra trạng thái đơn thuê
      String status = rentalData['status'];
      if (status != 'Ongoing') {
        throw Exception(
          'Chỉ có thể hủy đơn thuê đang trong trạng thái Ongoing',
        );
      }

      // Kiểm tra thời gian tạo đơn
      Timestamp createdAtTimestamp = rentalData['createdAt'];
      DateTime createdAt = createdAtTimestamp.toDate();
      DateTime now = DateTime.now();
      Duration difference = now.difference(createdAt);

      // Chỉ cho phép hủy trong vòng 30 phút
      if (difference.inMinutes >= 30) {
        throw Exception(
          'Chỉ có thể hủy đơn thuê trong vòng 30 phút từ khi tạo',
        );
      }

      // Lấy thông tin xe và số lượng đã thuê
      String bikeId = rentalData['bikeId'];
      int rentedQuantity = rentalData['quantity'] ?? 1;

      // Cập nhật trạng thái đơn thuê thành Cancelled
      await rentalsCollection.doc(id).update({
        'status': 'Cancelled',
        'cancelledAt': DateTime.now(),
      });

      // Cập nhật số lượng xe trong collection 'bikes'
      await FirebaseFirestore.instance.collection('bikes').doc(bikeId).update({
        'quantity': FieldValue.increment(rentedQuantity),
      });
    } catch (e) {
      print('Error cancelling rental: $e');
      throw e;
    }
  }

  // Kiểm tra và cập nhật trạng thái đơn thuê quá hạn
  Future<List<Rental>> checkAndUpdateExpiredRentals() async {
    try {
      final now = DateTime.now();

      // Lấy tất cả đơn thuê đã quá hạn nhưng vẫn có trạng thái 'Ongoing'
      QuerySnapshot querySnapshot =
          await rentalsCollection
              .where('endTime', isLessThan: Timestamp.fromDate(now))
              .where('status', isEqualTo: 'Ongoing')
              .get();

      List<Rental> expiredRentals = [];

      // Cập nhật trạng thái của các đơn thuê quá hạn
      for (var doc in querySnapshot.docs) {
        await rentalsCollection.doc(doc.id).update({'status': 'Expired'});
        Map<String, dynamic> data = doc.data() as Map<String, dynamic>;
        expiredRentals.add(Rental.fromMap(doc.id, data));
      }

      return expiredRentals;
    } catch (e) {
      print('Error checking expired rentals: $e');
      throw e;
    }
  }
}
