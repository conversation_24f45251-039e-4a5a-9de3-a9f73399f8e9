// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyApJ9n1AjSJTz349v-DuWnQaa7FpfnXt6I',
    appId: '1:143257094004:web:7dd72d961178c9f5380e21',
    messagingSenderId: '143257094004',
    projectId: 'bike-rental-140',
    authDomain: 'bike-rental-140.firebaseapp.com',
    storageBucket: 'bike-rental-140.firebasestorage.app',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyBaHPtrBRdanJpZjgblWT7xaZNNvyCeJj4',
    appId: '1:143257094004:android:b749449fa1590f90380e21',
    messagingSenderId: '143257094004',
    projectId: 'bike-rental-140',
    storageBucket: 'bike-rental-140.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyC2iMWUpHKapS4T7idUZ0WroBRIdnNkqjQ',
    appId: '1:143257094004:ios:d24de069806c75ee380e21',
    messagingSenderId: '143257094004',
    projectId: 'bike-rental-140',
    storageBucket: 'bike-rental-140.firebasestorage.app',
    iosBundleId: 'com.example.bikeRentalApp',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyC2iMWUpHKapS4T7idUZ0WroBRIdnNkqjQ',
    appId: '1:143257094004:ios:d24de069806c75ee380e21',
    messagingSenderId: '143257094004',
    projectId: 'bike-rental-140',
    storageBucket: 'bike-rental-140.firebasestorage.app',
    iosBundleId: 'com.example.bikeRentalApp',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyApJ9n1AjSJTz349v-DuWnQaa7FpfnXt6I',
    appId: '1:143257094004:web:f90d5fd7ee191cb7380e21',
    messagingSenderId: '143257094004',
    projectId: 'bike-rental-140',
    authDomain: 'bike-rental-140.firebaseapp.com',
    storageBucket: 'bike-rental-140.firebasestorage.app',
  );

}