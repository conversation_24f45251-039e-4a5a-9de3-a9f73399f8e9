import 'package:awesome_snackbar_content/awesome_snackbar_content.dart';
import 'package:bike_rental_app/models/user.dart';
import 'package:bike_rental_app/services/rental_service.dart';
import 'package:bike_rental_app/utils/animation_helper.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:loading_animation_widget/loading_animation_widget.dart';

class UserRentalHistoryScreen extends StatefulWidget {
  final User user;

  const UserRentalHistoryScreen({super.key, required this.user});

  @override
  _UserRentalHistoryScreenState createState() =>
      _UserRentalHistoryScreenState();
}

class _UserRentalHistoryScreenState extends State<UserRentalHistoryScreen> {
  final RentalService _rentalService = RentalService();
  bool isLoading = true;
  List<Map<String, dynamic>> rentals = [];

  @override
  void initState() {
    super.initState();
    _loadRentalHistory();
  }

  Future<void> _loadRentalHistory() async {
    try {
      final userRentals = await _rentalService.getUserRentals(widget.user.id);
      setState(() {
        rentals = userRentals;
        isLoading = false;
      });
    } catch (e) {
      setState(() => isLoading = false);
      final snackBar = SnackBar(
        elevation: 0,
        backgroundColor: Colors.transparent,
        behavior: SnackBarBehavior.floating,
        content: AwesomeSnackbarContent(
          title: 'Lỗi',
          message: 'Lỗi khi tải lịch sử thuê xe',
          contentType: ContentType.failure,
        ),
      );
      ScaffoldMessenger.of(context)
        ..hideCurrentSnackBar()
        ..showSnackBar(snackBar);
    }
  }

  String _getStatusText(String status) {
    switch (status) {
      case 'Ongoing':
        return 'Đang thuê';
      case 'Completed':
        return 'Đã hoàn thành';
      case 'Expired':
        return 'Quá hạn';
      case 'Cancelled':
        return 'Đã hủy';
      default:
        return 'Không xác định';
    }
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'Ongoing':
        return Colors.orange;
      case 'Completed':
        return Colors.green;
      case 'Expired':
        return Colors.red;
      case 'Cancelled':
        return Colors.grey;
      default:
        return Colors.grey;
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Lịch sử thuê xe',
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        bottom: PreferredSize(
          preferredSize: Size.fromHeight(1.0),
          child: Container(
            color: theme.colorScheme.primary.withOpacity(0.2),
            height: 1.0,
          ),
        ),
      ),
      body:
          isLoading
              ? Center(
                child: LoadingAnimationWidget.fourRotatingDots(
                  color: theme.colorScheme.primary,
                  size: 40,
                ),
              )
              : rentals.isEmpty
              ? Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.history_outlined,
                      size: 80,
                      color: theme.colorScheme.outline,
                    ),
                    SizedBox(height: 16),
                    Text(
                      'Chưa có lịch sử thuê xe',
                      style: theme.textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              )
              : RefreshIndicator(
                onRefresh: _loadRentalHistory,
                child: ListView.builder(
                  padding: EdgeInsets.all(16),
                  itemCount: rentals.length,
                  itemBuilder: (context, index) {
                    final rental = rentals[index];
                    return AnimationHelper.fadeInUp(
                      duration: Duration(milliseconds: 500),
                      delay: Duration(milliseconds: index * 100),
                      child: Card(
                        elevation: 3,
                        margin: EdgeInsets.only(bottom: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Column(
                          children: [
                            Container(
                              padding: EdgeInsets.all(16),
                              decoration: BoxDecoration(
                                color: theme.colorScheme.surfaceVariant,
                                borderRadius: BorderRadius.only(
                                  topLeft: Radius.circular(12),
                                  topRight: Radius.circular(12),
                                ),
                              ),
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    'Mã đơn: #${rental['id']}',
                                    style: theme.textTheme.titleMedium
                                        ?.copyWith(fontWeight: FontWeight.bold),
                                  ),
                                  Container(
                                    padding: EdgeInsets.symmetric(
                                      horizontal: 12,
                                      vertical: 6,
                                    ),
                                    decoration: BoxDecoration(
                                      color: _getStatusColor(
                                        rental['status'],
                                      ).withOpacity(0.1),
                                      borderRadius: BorderRadius.circular(20),
                                      border: Border.all(
                                        color: _getStatusColor(
                                          rental['status'],
                                        ),
                                      ),
                                    ),
                                    child: Text(
                                      _getStatusText(rental['status']),
                                      style: TextStyle(
                                        color: _getStatusColor(
                                          rental['status'],
                                        ),
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            Padding(
                              padding: EdgeInsets.all(16),
                              child: Column(
                                children: [
                                  _buildInfoRow(
                                    'Xe thuê',
                                    rental['bikeId'] ?? 'Không xác định',
                                    Icons.motorcycle,
                                    theme,
                                  ),
                                  _buildInfoRow(
                                    'Thời gian bắt đầu',
                                    DateFormat('dd/MM/yyyy HH:mm').format(
                                      (rental['startTime'] as Timestamp)
                                          .toDate(),
                                    ),
                                    Icons.access_time,
                                    theme,
                                  ),
                                  _buildInfoRow(
                                    'Thời gian kết thúc',
                                    rental['endTime'] != null
                                        ? DateFormat('dd/MM/yyyy HH:mm').format(
                                          (rental['endTime'] as Timestamp)
                                              .toDate(),
                                        )
                                        : 'Chưa kết thúc',
                                    Icons.access_time_filled,
                                    theme,
                                  ),
                                  _buildInfoRow(
                                    'Ngày trả xe',
                                    rental['returnedDate'] != null
                                        ? DateFormat('dd/MM/yyyy').format(
                                          (rental['returnedDate'] as Timestamp)
                                              .toDate(),
                                        )
                                        : rental['status'] == 'Cancelled'
                                        ? 'Đã hủy'
                                        : 'Chưa kết thúc',
                                    Icons.calendar_today,
                                    theme,
                                  ),
                                  if (rental['cancelledAt'] != null)
                                    _buildInfoRow(
                                      'Ngày hủy',
                                      DateFormat('dd/MM/yyyy HH:mm').format(
                                        (rental['cancelledAt'] as Timestamp)
                                            .toDate(),
                                      ),
                                      Icons.cancel_outlined,
                                      theme,
                                    ),
                                  _buildInfoRow(
                                    'Tổng tiền',
                                    NumberFormat.currency(
                                      locale: 'vi_VN',
                                      symbol: 'đ',
                                    ).format(rental['totalAmount'] ?? 0),
                                    Icons.attach_money,
                                    theme,
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ),
    );
  }

  Widget _buildInfoRow(
    String label,
    String value,
    IconData icon,
    ThemeData theme,
  ) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Icon(icon, size: 20, color: theme.colorScheme.outline),
          SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.outline,
                  ),
                ),
                SizedBox(height: 4),
                Text(
                  value,
                  style: theme.textTheme.bodyLarge?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
