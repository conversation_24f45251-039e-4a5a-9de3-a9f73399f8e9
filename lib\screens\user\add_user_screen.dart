import 'dart:io';
import 'package:awesome_snackbar_content/awesome_snackbar_content.dart';
import 'package:bike_rental_app/models/user.dart';
import 'package:bike_rental_app/services/storage_service.dart';
import 'package:bike_rental_app/services/user_service.dart';
import 'package:bike_rental_app/utils/responsive_helper.dart';
import 'package:bike_rental_app/widgets/custom_text_form_field.dart';
import 'package:bike_rental_app/widgets/responsive_layout.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:intl/intl.dart';
import 'package:loading_animation_widget/loading_animation_widget.dart';

class AddUserScreen extends StatefulWidget {
  const AddUserScreen({super.key});
  
  @override
  State<AddUserScreen> createState() => _AddUserScreenState();
}

class _AddUserScreenState extends State<AddUserScreen> {
  final UserService _userService = UserService();
  final StorageService _storageService = StorageService();
  final _formKey = GlobalKey<FormState>();
  final ImagePicker _picker = ImagePicker();

  String name = '';
  String email = '';
  String phone = '';
  String address = '';
  String idCard = '';
  DateTime dateOfBirth = DateTime.now().subtract(
    const Duration(days: 365 * 18),
  ); // Mặc định là 18 tuổi trước
  bool _isLoading = false;
  File? _imageFile;

  Future<void> _pickImage() async {
    final XFile? pickedFile = await _picker.pickImage(
      source: ImageSource.gallery,
      maxWidth: 512,
      maxHeight: 512,
      imageQuality: 85,
    );

    if (pickedFile != null) {
      setState(() {
        _imageFile = File(pickedFile.path);
      });
    }
  }

  Future<String?> _uploadImage() async {
    if (_imageFile == null) return null;

    try {
      final String fileName =
          'users/${DateTime.now().millisecondsSinceEpoch}.jpg';
      final String downloadUrl = await _storageService.uploadFile(
        _imageFile!,
        fileName,
      );
      return downloadUrl;
    } catch (e) {
      if (!mounted) return null;
      
      final snackBar = SnackBar(
        elevation: 0,
        backgroundColor: Colors.transparent,
        behavior: SnackBarBehavior.floating,
        content: AwesomeSnackbarContent(
          title: 'Lỗi',
          message: 'Đã xảy ra lỗi khi tải ảnh lên',
          contentType: ContentType.failure,
        ),
      );
      ScaffoldMessenger.of(context)
        ..hideCurrentSnackBar()
        ..showSnackBar(snackBar);
      return null;
    }
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: dateOfBirth,
      firstDate: DateTime(1950),
      lastDate: DateTime.now(),
      locale: const Locale('en', 'US'),
    );
    if (picked != null && picked != dateOfBirth) {
      setState(() {
        dateOfBirth = picked;
      });
    }
  }

  Future<void> _addUser() async {
    if (!_formKey.currentState!.validate()) return;
    
    setState(() {
      _isLoading = true;
    });

    try {
      final String? imageUrl = await _uploadImage();

      if (!mounted) return;
      
      final user = User(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        name: name,
        email: email,
        phone: phone,
        imageUrl: imageUrl,
        address: address,
        idCard: idCard,
        dateOfBirth: dateOfBirth,
      );

      await _userService.addUser(user);

      if (!mounted) return;
      
      final snackBar = SnackBar(
        elevation: 0,
        backgroundColor: Colors.transparent,
        behavior: SnackBarBehavior.floating,
        content: AwesomeSnackbarContent(
          title: 'Thành công',
          message: 'Người dùng đã được thêm thành công',
          contentType: ContentType.success,
        ),
      );
      
      ScaffoldMessenger.of(context)
        ..hideCurrentSnackBar()
        ..showSnackBar(snackBar);

      Navigator.pop(context);
    } catch (e) {
      if (!mounted) return;
      
      final snackBar = SnackBar(
        elevation: 0,
        backgroundColor: Colors.transparent,
        behavior: SnackBarBehavior.floating,
        content: AwesomeSnackbarContent(
          title: 'Lỗi',
          message: 'Đã xảy ra lỗi khi thêm người dùng',
          contentType: ContentType.failure,
        ),
      );
      
      ScaffoldMessenger.of(context)
        ..hideCurrentSnackBar()
        ..showSnackBar(snackBar);
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // Widget cho form thêm người dùng
  Widget _buildUserForm() {
    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Text(
            'Thông tin người dùng',
            style: TextStyle(
              fontSize: ResponsiveHelper.adaptiveFontSize(context, 20),
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.primary,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),

          // Profile image
          Center(
            child: GestureDetector(
              onTap: _pickImage,
              child: Stack(
                children: [
                  CircleAvatar(
                    radius: 60,
                    backgroundColor: Theme.of(context).colorScheme.surface,
                    backgroundImage: _imageFile != null
                        ? FileImage(_imageFile!) as ImageProvider
                        : const AssetImage('assets/images/default_avatar.jpg'),
                  ),
                  Positioned(
                    bottom: 0,
                    right: 0,
                    child: Container(
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.surface,
                        shape: BoxShape.circle,
                      ),
                      padding: const EdgeInsets.all(8),
                      child: Icon(
                        Icons.camera_alt,
                        color: Theme.of(context).colorScheme.primary,
                        size: 20,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 24),

          CustomTextFormField(
            label: 'Họ và tên',
            prefixIcon: Icons.person,
            onChanged: (val) => setState(() => name = val),
            validator: (val) => val!.isEmpty ? 'Vui lòng nhập tên' : null,
          ),
          const SizedBox(height: 16),

          CustomTextFormField(
            label: 'Email',
            prefixIcon: Icons.email,
            keyboardType: TextInputType.emailAddress,
            onChanged: (val) => setState(() => email = val),
            validator: (val) {
              if (val!.isEmpty) {
                return 'Vui lòng nhập email';
              }
              bool emailValid = RegExp(
                r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$',
              ).hasMatch(val);
              return emailValid ? null : 'Email không hợp lệ';
            },
          ),
          const SizedBox(height: 16),

          CustomTextFormField(
            label: 'Số điện thoại',
            prefixIcon: Icons.phone,
            keyboardType: TextInputType.phone,
            onChanged: (val) => setState(() => phone = val),
            validator: (val) {
              if (val!.isEmpty) {
                return 'Vui lòng nhập số điện thoại';
              }
              bool phoneValid = RegExp(
                r'^\+?[0-9]{10,12}$',
              ).hasMatch(val);
              return phoneValid ? null : 'Số điện thoại không hợp lệ';
            },
          ),
          const SizedBox(height: 16),

          CustomTextFormField(
            label: 'Địa chỉ',
            prefixIcon: Icons.location_on,
            onChanged: (val) => setState(() => address = val),
            validator: (val) => val!.isEmpty ? 'Vui lòng nhập địa chỉ' : null,
          ),
          const SizedBox(height: 16),

          CustomTextFormField(
            label: 'Số CCCD/CMND',
            prefixIcon: Icons.credit_card,
            keyboardType: TextInputType.number,
            onChanged: (val) => setState(() => idCard = val),
            validator: (val) {
              if (val!.isEmpty) {
                return 'Vui lòng nhập số CCCD/CMND';
              }
              bool idCardValid = RegExp(
                r'^[0-9]{9,12}$',
              ).hasMatch(val);
              return idCardValid ? null : 'Số CCCD/CMND không hợp lệ';
            },
          ),
          const SizedBox(height: 16),

          // Date of birth picker
          InkWell(
            onTap: () => _selectDate(context),
            child: InputDecorator(
              decoration: InputDecoration(
                labelText: 'Ngày sinh',
                prefixIcon: Icon(
                  Icons.calendar_today,
                  color: Theme.of(context).colorScheme.primary,
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10),
                  borderSide: BorderSide(
                    color: Theme.of(context).colorScheme.primary.withAlpha(128),
                  ),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10),
                  borderSide: BorderSide(
                    color: Theme.of(context).colorScheme.primary.withAlpha(128),
                  ),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    DateFormat('dd/MM/yyyy').format(dateOfBirth),
                    style: TextStyle(
                      fontSize: ResponsiveHelper.adaptiveFontSize(context, 16),
                    ),
                  ),
                  Icon(
                    Icons.arrow_drop_down,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 32),

          ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.primary,
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
            ),
            onPressed: _isLoading ? null : _addUser,
            child: _isLoading
                ? LoadingAnimationWidget.fourRotatingDots(
                    color: Theme.of(context).colorScheme.onPrimary,
                    size: 20,
                  )
                : Text(
                    'Thêm người dùng',
                    style: TextStyle(
                      fontSize: ResponsiveHelper.adaptiveFontSize(context, 16),
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).colorScheme.onPrimary,
                    ),
                  ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final isLandscape = ResponsiveHelper.isLandscape(context);
    
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Thêm người dùng mới',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
            color: Theme.of(context).colorScheme.primary,
          ),
        ),
        backgroundColor: Theme.of(context).colorScheme.onPrimary,
        elevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: Theme.of(context).colorScheme.primary,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(1.0),
          child: Container(
            color: Theme.of(context).colorScheme.primary.withAlpha(51),
            height: 1.0,
          ),
        ),
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Theme.of(context).colorScheme.onPrimary,
              Theme.of(context).colorScheme.primary,
            ],
            stops: const [0.0, 0.9],
          ),
        ),
        child: SafeArea(
          child: ResponsiveLayout(
            mobile: SingleChildScrollView(
              padding: ResponsiveHelper.adaptivePadding(context),
              child: Card(
                elevation: 4,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Padding(
                  padding: ResponsiveHelper.adaptivePadding(context),
                  child: _buildUserForm(),
                ),
              ),
            ),
            tablet: Center(
              child: SizedBox(
                width: isLandscape ? 700 : 500,
                child: SingleChildScrollView(
                  padding: ResponsiveHelper.adaptivePadding(context),
                  child: Card(
                    elevation: 4,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Padding(
                      padding: ResponsiveHelper.adaptivePadding(context),
                      child: _buildUserForm(),
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
